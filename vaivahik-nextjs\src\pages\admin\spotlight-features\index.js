import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  Switch,
  TextField,
  Typography,
  Chip,
  Tooltip,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Badge
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  People as PeopleIcon,
  AttachMoney as AttachMoneyIcon,
  AccessTime as AccessTimeIcon,
  BarChart as BarChartIcon,
  FlashOn as FlashOnIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';

export default function SpotlightFeaturesPage() {
  const router = useRouter();
  const [features, setFeatures] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openModal, setOpenModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [openUsersModal, setOpenUsersModal] = useState(false);
  const [currentFeature, setCurrentFeature] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    discountPercent: '',
    discountedPrice: '',
    defaultCount: 1,
    isActive: true
  });
  const [featureToDelete, setFeatureToDelete] = useState(null);
  const [activeUsers, setActiveUsers] = useState([]);
  const [analyticsData, setAnalyticsData] = useState({
    totalFeatures: 0,
    totalPurchases: 0,
    activeSpotlights: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    fetchFeatures();
  }, []);

  const fetchFeatures = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const queryParams = new URLSearchParams({
        page: 1,
        limit: 100, // Get all features for now
        search: '',
        sortBy: 'createdAt',
        order: 'desc'
      }).toString();

      const response = await fetch(`/api/admin/spotlight/features?${queryParams}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        setFeatures(data.features || []);

        // Calculate analytics
        const analytics = {
          totalFeatures: data.features ? data.features.length : 0,
          totalPurchases: data.features ? data.features.reduce((sum, feature) => sum + (feature.purchaseCount || 0), 0) : 0,
          activeSpotlights: data.features ? data.features.reduce((sum, feature) => sum + (feature.activeCount || 0), 0) : 0,
          totalRevenue: data.features ? data.features.reduce((sum, feature) => sum + (feature.revenue || 0), 0) : 0
        };
        setAnalyticsData(analytics);
      } else {
        throw new Error(data.message || 'Failed to fetch spotlight features');
      }
      setLoading(false);
    } catch (error) {
      console.error('Error fetching spotlight features:', error);
      toast.error('Error fetching spotlight features: ' + error.message);
      setLoading(false);
    }
  };

  const handleOpenModal = (feature = null) => {
    if (feature) {
      setCurrentFeature(feature);
      setFormData({
        name: feature.name,
        description: feature.description || '',
        price: feature.price,
        discountPercent: feature.discountPercent || '',
        discountedPrice: feature.discountedPrice || '',
        defaultCount: feature.defaultCount || 1,
        isActive: feature.isActive
      });
    } else {
      setCurrentFeature(null);
      setFormData({
        name: '',
        description: '',
        price: '',
        discountPercent: '',
        discountedPrice: '',
        defaultCount: 1,
        isActive: true
      });
    }
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Calculate discounted price if price or discount percent changes
    if (name === 'price' || name === 'discountPercent') {
      if (formData.price && formData.discountPercent) {
        const price = parseFloat(name === 'price' ? value : formData.price);
        const discount = parseFloat(name === 'discountPercent' ? value : formData.discountPercent);
        if (!isNaN(price) && !isNaN(discount)) {
          const discountedPrice = price - (price * (discount / 100));
          setFormData(prev => ({ ...prev, discountedPrice: discountedPrice.toFixed(2) }));
        }
      }
    }
  };

  const handleCheckboxChange = (e) => {
    setFormData(prev => ({ ...prev, [e.target.name]: e.target.checked }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      const dataToSend = {
        name: formData.name,
        description: formData.description,
        price: parseFloat(formData.price),
        discountPercent: formData.discountPercent ? parseInt(formData.discountPercent) : null,
        defaultCount: parseInt(formData.defaultCount) || 1,
        isActive: formData.isActive
      };

      let response;
      if (currentFeature) {
        // Update existing feature
        response = await fetch(`/api/admin/spotlight/features/${currentFeature.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(dataToSend)
        });
      } else {
        // Create new feature
        response = await fetch('/api/admin/spotlight/features', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(dataToSend)
        });
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        toast.success(currentFeature ? 'Spotlight feature updated successfully' : 'Spotlight feature created successfully');
        handleCloseModal();
        fetchFeatures();
      } else {
        throw new Error(data.message || 'Operation failed');
      }
    } catch (error) {
      console.error('Error saving spotlight feature:', error);
      toast.error('Error: ' + error.message);
    }
  };

  const handleDeleteClick = (feature) => {
    setFeatureToDelete(feature);
    setOpenDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      const response = await fetch(`/api/admin/spotlight/features/${featureToDelete.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        toast.success('Spotlight feature deleted successfully');
        setOpenDeleteModal(false);
        fetchFeatures();
      } else {
        throw new Error(data.message || 'Failed to delete feature');
      }
    } catch (error) {
      console.error('Error deleting spotlight feature:', error);
      toast.error('Error: ' + error.message);
    }
  };

  const handleToggleStatus = async (featureId, currentStatus) => {
    try {
      const response = await fetch(`/api/admin/spotlight/features/${featureId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isActive: !currentStatus
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        toast.success(`Spotlight feature ${currentStatus ? 'deactivated' : 'activated'} successfully`);
        fetchFeatures();
      } else {
        throw new Error(data.message || 'Failed to update feature status');
      }
    } catch (error) {
      console.error('Error toggling spotlight feature status:', error);
      toast.error('Error: ' + error.message);
    }
  };

  const handleViewUsers = async (feature) => {
    try {
      setCurrentFeature(feature);

      const response = await fetch(`/api/admin/spotlight/active-users?featureId=${feature.id}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        setActiveUsers(data.activeUsers || []);
        setOpenUsersModal(true);
      } else {
        throw new Error(data.message || 'Failed to fetch active users');
      }
    } catch (error) {
      console.error('Error fetching spotlight users:', error);
      toast.error('Error: ' + error.message);
    }
  };

  return (
    <EnhancedAdminLayout title="Spotlight Features">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Spotlight Features
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenModal()}
          >
            Add New Spotlight Option
          </Button>
        </Box>

        {/* Analytics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Features
                </Typography>
                <Typography variant="h4">
                  {analyticsData.totalFeatures}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Purchases
                </Typography>
                <Typography variant="h4">
                  {analyticsData.totalPurchases}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Active Spotlights
                </Typography>
                <Typography variant="h4">
                  {analyticsData.activeSpotlights}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Revenue
                </Typography>
                <Typography variant="h4">
                  ₹{analyticsData.totalRevenue.toFixed(2)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={3}>
            {features.map(feature => (
              <Grid item xs={12} sm={6} md={4} key={feature.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography gutterBottom variant="h5" component="h2">
                        {feature.name}
                      </Typography>
                      <Chip
                        label={feature.isActive ? 'Active' : 'Inactive'}
                        color={feature.isActive ? 'success' : 'error'}
                        size="small"
                      />
                    </Box>

                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {feature.description || 'No description provided'}
                    </Typography>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <AccessTimeIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body2">
                        Duration: 24 hours
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <FlashOnIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body2">
                        Default Count: {feature.defaultCount || 1}
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      {feature.discountedPrice ? (
                        <>
                          <Typography variant="body2" color="text.secondary" sx={{ textDecoration: 'line-through', mr: 1 }}>
                            ₹{feature.price}
                          </Typography>
                          <Typography variant="body1" color="primary" fontWeight="bold">
                            ₹{feature.discountedPrice}
                          </Typography>
                          <Chip
                            label={`-${feature.discountPercent}%`}
                            size="small"
                            color="secondary"
                            sx={{ ml: 1 }}
                          />
                        </>
                      ) : (
                        <Typography variant="body1" fontWeight="bold">
                          ₹{feature.price}
                        </Typography>
                      )}
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                      <Box>
                        <Tooltip title="Total Purchases">
                          <Chip
                            icon={<AttachMoneyIcon />}
                            label={feature.purchaseCount || 0}
                            size="small"
                            sx={{ mr: 1 }}
                          />
                        </Tooltip>
                        <Tooltip title="Active Now">
                          <Chip
                            icon={<PeopleIcon />}
                            label={feature.activeCount || 0}
                            size="small"
                            color="primary"
                          />
                        </Tooltip>
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        Revenue: ₹{feature.revenue || 0}
                      </Typography>
                    </Box>
                  </CardContent>
                  <Divider />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                    <Box>
                      <Tooltip title="View Active Users">
                        <IconButton
                          onClick={() => handleViewUsers(feature)}
                          disabled={!feature.activeCount}
                        >
                          <Badge badgeContent={feature.activeCount} color="primary">
                            <VisibilityIcon />
                          </Badge>
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton onClick={() => handleOpenModal(feature)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton onClick={() => handleDeleteClick(feature)}>
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={feature.isActive}
                          onChange={() => handleToggleStatus(feature.id, feature.isActive)}
                          color="primary"
                        />
                      }
                      label=""
                    />
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>

      {/* Add/Edit Feature Modal */}
      <Dialog open={openModal} onClose={handleCloseModal} maxWidth="sm" fullWidth>
        <DialogTitle>{currentFeature ? 'Edit Spotlight Feature' : 'Add New Spotlight Feature'}</DialogTitle>
        <form onSubmit={handleSubmit}>
          <DialogContent>
            <TextField
              name="name"
              label="Feature Name"
              value={formData.name}
              onChange={handleChange}
              fullWidth
              required
              margin="normal"
            />
            <TextField
              name="description"
              label="Description"
              value={formData.description}
              onChange={handleChange}
              fullWidth
              multiline
              rows={3}
              margin="normal"
              required
            />
            <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
              <TextField
                name="price"
                label="Regular Price (₹)"
                type="number"
                value={formData.price}
                onChange={handleChange}
                fullWidth
                required
                margin="normal"
                InputProps={{
                  startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                }}
              />
              <TextField
                name="discountPercent"
                label="Discount (%)"
                type="number"
                value={formData.discountPercent}
                onChange={handleChange}
                fullWidth
                margin="normal"
                InputProps={{
                  endAdornment: <InputAdornment position="end">%</InputAdornment>,
                }}
              />
            </Box>
            {formData.discountedPrice && (
              <TextField
                name="discountedPrice"
                label="Discounted Price (₹)"
                value={formData.discountedPrice}
                fullWidth
                margin="normal"
                InputProps={{
                  startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                  readOnly: true,
                }}
              />
            )}

            <TextField
              name="defaultCount"
              label="Default Count (spotlights per purchase)"
              type="number"
              value={formData.defaultCount}
              onChange={handleChange}
              fullWidth
              required
              margin="normal"
              inputProps={{ min: 1, max: 100 }}
              helperText="Number of spotlights users will receive when purchasing this package"
            />
            <FormControlLabel
              control={
                <Switch
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleCheckboxChange}
                  color="primary"
                />
              }
              label="Active"
              sx={{ mt: 2 }}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseModal}>Cancel</Button>
            <Button type="submit" variant="contained" color="primary">
              {currentFeature ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={openDeleteModal} onClose={() => setOpenDeleteModal(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the spotlight feature "{featureToDelete?.name}"? This action cannot be undone.
            {featureToDelete?.activeCount > 0 && (
              <Typography color="error" sx={{ mt: 2 }}>
                Warning: There are currently {featureToDelete.activeCount} users with active spotlights using this feature.
                Deleting it may affect their experience.
              </Typography>
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteModal(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Active Users Modal */}
      <Dialog open={openUsersModal} onClose={() => setOpenUsersModal(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Users with Active {currentFeature?.name} Spotlights
        </DialogTitle>
        <DialogContent>
          {activeUsers.length > 0 ? (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>User</TableCell>
                    <TableCell>Contact</TableCell>
                    <TableCell>Start Time</TableCell>
                    <TableCell>End Time</TableCell>
                    <TableCell>Time Remaining</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {activeUsers.map(user => (
                    <TableRow key={user.spotlightId}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Avatar src={user.profilePic} sx={{ mr: 2 }}>
                            {user.name.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {user.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {user.gender}, {user.age}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">{user.email}</Typography>
                        <Typography variant="body2">{user.phone}</Typography>
                      </TableCell>
                      <TableCell>
                        {new Date(user.startTime).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        {new Date(user.endTime).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.timeRemaining}
                          color="primary"
                          size="small"
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          ) : (
            <Typography>No users currently have active spotlights for this feature.</Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenUsersModal(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </EnhancedAdminLayout>
  );
}
