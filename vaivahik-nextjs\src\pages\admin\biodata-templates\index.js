import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import EnhancedAdminLayout from '@/components/admin/EnhancedAdminLayout';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardMedia,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControlLabel,
  Grid,
  IconButton,
  InputAdornment,
  Switch,
  TextField,
  Typography,
  Chip,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  CloudUpload as CloudUploadIcon,
  AttachMoney as AttachMoneyIcon,
  BarChart as BarChartIcon
} from '@mui/icons-material';
import axios from 'axios';
import { toast } from 'react-toastify';

export default function BiodataTemplatesPage() {
  const router = useRouter();
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const [openModal, setOpenModal] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    discountPercent: '',
    discountedPrice: '',
    isActive: true
  });
  const [previewImage, setPreviewImage] = useState(null);
  const [designFile, setDesignFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState('');
  const [designFileName, setDesignFileName] = useState('');
  const [templateToDelete, setTemplateToDelete] = useState(null);
  const [analyticsData, setAnalyticsData] = useState({
    totalTemplates: 0,
    totalPurchases: 0,
    totalDownloads: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const queryParams = new URLSearchParams({
        page: 1,
        limit: 100, // Get all templates for now
        search: '',
        sortBy: 'createdAt',
        order: 'desc'
      }).toString();

      console.log('Fetching biodata templates...');
      const response = await fetch(`/api/admin/biodata/templates?${queryParams}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('API response:', data);

      if (data.success) {
        setTemplates(data.templates || []);

        // Calculate analytics
        let analytics = {
          totalTemplates: data.templates ? data.templates.length : 0,
          totalPurchases: data.templates ? data.templates.reduce((sum, template) => sum + (template.purchaseCount || 0), 0) : 0,
          totalDownloads: data.templates ? data.templates.reduce((sum, template) => sum + (template.downloadCount || 0), 0) : 0,
          totalRevenue: data.templates ? data.templates.reduce((sum, template) => sum + (template.revenue || 0), 0) : 0
        };

        // If stats are provided in the response, use them
        if (data.stats) {
          console.log('Using stats from API response:', data.stats);
          analytics = {
            totalTemplates: data.stats.totalTemplates || analytics.totalTemplates,
            totalPurchases: data.stats.totalPurchases || analytics.totalPurchases,
            totalDownloads: data.stats.totalDownloads || analytics.totalDownloads,
            totalRevenue: data.stats.totalRevenue || analytics.totalRevenue
          };
        }

        setAnalyticsData(analytics);
      } else {
        throw new Error(data.message || 'Failed to fetch templates');
      }
      setLoading(false);
    } catch (error) {
      console.error('Error fetching templates:', error);
      toast.error('Error fetching templates: ' + error.message);
      setLoading(false);
    }
  };

  const handleOpenModal = (template = null) => {
    if (template) {
      setCurrentTemplate(template);
      setFormData({
        name: template.name,
        description: template.description || '',
        price: template.price,
        discountPercent: template.discountPercent || '',
        discountedPrice: template.discountedPrice || '',
        isActive: template.isActive
      });
      setPreviewUrl(template.previewImage);
    } else {
      setCurrentTemplate(null);
      setFormData({
        name: '',
        description: '',
        price: '',
        discountPercent: '',
        discountedPrice: '',
        isActive: true
      });
      setPreviewUrl('');
      setDesignFileName('');
    }
    setOpenModal(true);
  };

  const handleCloseModal = () => {
    setOpenModal(false);
    setPreviewImage(null);
    setDesignFile(null);
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Calculate discounted price if price or discount percent changes
    if (name === 'price' || name === 'discountPercent') {
      if (formData.price && formData.discountPercent) {
        const price = parseFloat(name === 'price' ? value : formData.price);
        const discount = parseFloat(name === 'discountPercent' ? value : formData.discountPercent);
        if (!isNaN(price) && !isNaN(discount)) {
          const discountedPrice = price - (price * (discount / 100));
          setFormData(prev => ({ ...prev, discountedPrice: discountedPrice.toFixed(2) }));
        }
      }
    }
  };

  const handleCheckboxChange = (e) => {
    setFormData(prev => ({ ...prev, [e.target.name]: e.target.checked }));
  };

  const handleFileChange = (e) => {
    const { name, files } = e.target;
    if (files && files[0]) {
      if (name === 'previewImage') {
        setPreviewImage(files[0]);
        setPreviewUrl(URL.createObjectURL(files[0]));
      } else if (name === 'designFile') {
        setDesignFile(files[0]);
        setDesignFileName(files[0].name);
      }
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Create FormData object for file upload
      const formDataObj = new FormData();
      formDataObj.append('name', formData.name);
      formDataObj.append('description', formData.description);
      formDataObj.append('price', formData.price);
      formDataObj.append('isActive', formData.isActive);

      if (formData.discountPercent) {
        formDataObj.append('discountPercent', formData.discountPercent);
      }

      if (previewImage) {
        formDataObj.append('previewImage', previewImage);
      }

      if (designFile) {
        formDataObj.append('designFile', designFile);
      }

      let response;

      if (currentTemplate) {
        // Update existing template
        response = await fetch(`/api/admin/biodata/templates/${currentTemplate.id}`, {
          method: 'PUT',
          body: formDataObj
        });
      } else {
        // Create new template
        response = await fetch('/api/admin/biodata/templates', {
          method: 'POST',
          body: formDataObj
        });
      }

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        toast.success(currentTemplate ? 'Template updated successfully' : 'Template created successfully');
        handleCloseModal();
        fetchTemplates();
      } else {
        throw new Error(data.message || 'Operation failed');
      }
    } catch (error) {
      console.error('Error saving template:', error);
      toast.error('Error: ' + error.message);
    }
  };

  const handleDeleteClick = (template) => {
    setTemplateToDelete(template);
    setOpenDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      const response = await fetch(`/api/admin/biodata/templates/${templateToDelete.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        toast.success('Template deleted successfully');
        setOpenDeleteModal(false);
        fetchTemplates();
      } else {
        throw new Error(data.message || 'Failed to delete template');
      }
    } catch (error) {
      console.error('Error deleting template:', error);
      toast.error('Error: ' + error.message);
    }
  };

  const handleToggleStatus = async (templateId, currentStatus) => {
    try {
      const formDataObj = new FormData();
      formDataObj.append('isActive', !currentStatus);

      const response = await fetch(`/api/admin/biodata/templates/${templateId}`, {
        method: 'PUT',
        body: formDataObj
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (data.message) {
        toast.success(`Template ${currentStatus ? 'deactivated' : 'activated'} successfully`);
        fetchTemplates();
      } else {
        throw new Error(data.message || 'Failed to update template status');
      }
    } catch (error) {
      console.error('Error toggling template status:', error);
      toast.error('Error: ' + error.message);
    }
  };

  const handlePreviewTemplate = (templateId) => {
    router.push(`/admin/biodata-templates/preview/${templateId}`);
  };

  return (
    <EnhancedAdminLayout title="Biodata Templates">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Biodata Templates
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => handleOpenModal()}
          >
            Add New Template
          </Button>
        </Box>

        {/* Analytics Cards */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Templates
                </Typography>
                <Typography variant="h4">
                  {analyticsData.totalTemplates}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Purchases
                </Typography>
                <Typography variant="h4">
                  {analyticsData.totalPurchases}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Downloads
                </Typography>
                <Typography variant="h4">
                  {analyticsData.totalDownloads}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Revenue
                </Typography>
                <Typography variant="h4">
                  ₹{analyticsData.totalRevenue.toFixed(2)}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={3}>
            {templates.map(template => (
              <Grid item xs={12} sm={6} md={4} key={template.id}>
                <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                  <Box sx={{ position: 'relative' }}>
                    <CardMedia
                      component="img"
                      height="200"
                      image={template.previewImage || template.thumbnail || '/img/template-placeholder.jpg'}
                      alt={template.name}
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = '/img/template-placeholder.jpg';
                      }}
                      sx={{ objectFit: 'cover' }}
                    />
                    <Box sx={{
                      position: 'absolute',
                      top: 10,
                      right: 10,
                      bgcolor: template.isActive ? 'success.main' : 'error.main',
                      color: 'white',
                      borderRadius: 1,
                      px: 1,
                      py: 0.5
                    }}>
                      {template.isActive ? 'Active' : 'Inactive'}
                    </Box>
                  </Box>
                  <CardContent sx={{ flexGrow: 1 }}>
                    <Typography gutterBottom variant="h5" component="h2">
                      {template.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {template.description || 'No description provided'}
                    </Typography>

                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      {template.isPremium ? (
                        <Typography variant="body1" fontWeight="bold">
                          ₹{template.price || 0}
                        </Typography>
                      ) : (
                        <Typography variant="body1" fontWeight="bold" color="success.main">
                          Free
                        </Typography>
                      )}
                      {template.discountedPrice && (
                        <>
                          <Typography variant="body2" color="text.secondary" sx={{ textDecoration: 'line-through', ml: 1, mr: 1 }}>
                            ₹{template.price}
                          </Typography>
                          <Typography variant="body1" color="primary" fontWeight="bold">
                            ₹{template.discountedPrice}
                          </Typography>
                          {template.discountPercent && (
                            <Chip
                              label={`-${template.discountPercent}%`}
                              size="small"
                              color="secondary"
                              sx={{ ml: 1 }}
                            />
                          )}
                        </>
                      )}
                    </Box>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
                      <Box>
                        {template.purchaseCount !== undefined && (
                          <Tooltip title="Purchases">
                            <Chip
                              icon={<AttachMoneyIcon />}
                              label={template.purchaseCount || 0}
                              size="small"
                              sx={{ mr: 1 }}
                            />
                          </Tooltip>
                        )}
                        <Tooltip title="Downloads">
                          <Chip
                            icon={<CloudUploadIcon />}
                            label={template.downloadCount || 0}
                            size="small"
                          />
                        </Tooltip>
                      </Box>
                      {template.revenue !== undefined && (
                        <Typography variant="body2" color="text.secondary">
                          Revenue: ₹{template.revenue || 0}
                        </Typography>
                      )}
                    </Box>
                  </CardContent>
                  <Divider />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', p: 1 }}>
                    <Box>
                      <Tooltip title="Preview">
                        <IconButton onClick={() => handlePreviewTemplate(template.id)}>
                          <VisibilityIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit">
                        <IconButton onClick={() => handleOpenModal(template)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete">
                        <IconButton onClick={() => handleDeleteClick(template)}>
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={template.isActive}
                          onChange={() => handleToggleStatus(template.id, template.isActive)}
                          color="primary"
                        />
                      }
                      label=""
                    />
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>

      {/* Add/Edit Template Modal */}
      <Dialog open={openModal} onClose={handleCloseModal} maxWidth="md" fullWidth>
        <DialogTitle>{currentTemplate ? 'Edit Template' : 'Add New Template'}</DialogTitle>
        <form onSubmit={handleSubmit}>
          <DialogContent>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  name="name"
                  label="Template Name"
                  value={formData.name}
                  onChange={handleChange}
                  fullWidth
                  required
                  margin="normal"
                />
                <TextField
                  name="description"
                  label="Description"
                  value={formData.description}
                  onChange={handleChange}
                  fullWidth
                  multiline
                  rows={3}
                  margin="normal"
                />
                <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                  <TextField
                    name="price"
                    label="Regular Price (₹)"
                    type="number"
                    value={formData.price}
                    onChange={handleChange}
                    fullWidth
                    required
                    margin="normal"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                    }}
                  />
                  <TextField
                    name="discountPercent"
                    label="Discount (%)"
                    type="number"
                    value={formData.discountPercent}
                    onChange={handleChange}
                    fullWidth
                    margin="normal"
                    InputProps={{
                      endAdornment: <InputAdornment position="end">%</InputAdornment>,
                    }}
                  />
                </Box>
                {formData.discountedPrice && (
                  <TextField
                    name="discountedPrice"
                    label="Discounted Price (₹)"
                    value={formData.discountedPrice}
                    fullWidth
                    margin="normal"
                    InputProps={{
                      startAdornment: <InputAdornment position="start">₹</InputAdornment>,
                      readOnly: true,
                    }}
                  />
                )}
                <FormControlLabel
                  control={
                    <Switch
                      name="isActive"
                      checked={formData.isActive}
                      onChange={handleCheckboxChange}
                      color="primary"
                    />
                  }
                  label="Active"
                  sx={{ mt: 2 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle1" gutterBottom>
                  Preview Image
                </Typography>
                <Box
                  sx={{
                    border: '1px dashed grey',
                    borderRadius: 1,
                    p: 1,
                    mb: 2,
                    height: 200,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  {previewUrl ? (
                    <img
                      src={previewUrl}
                      alt="Preview"
                      style={{ maxWidth: '100%', maxHeight: '100%', objectFit: 'contain' }}
                    />
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No preview image selected
                    </Typography>
                  )}
                </Box>
                <Button
                  variant="outlined"
                  component="label"
                  fullWidth
                  startIcon={<CloudUploadIcon />}
                >
                  Upload Preview Image
                  <input
                    type="file"
                    name="previewImage"
                    onChange={handleFileChange}
                    accept="image/*"
                    hidden
                  />
                </Button>

                <Typography variant="subtitle1" gutterBottom sx={{ mt: 3 }}>
                  Template Design File
                </Typography>
                <Box
                  sx={{
                    border: '1px dashed grey',
                    borderRadius: 1,
                    p: 2,
                    mb: 2,
                    minHeight: 60,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  {designFileName || (currentTemplate && currentTemplate.designFile) ? (
                    <Typography variant="body2">
                      {designFileName || currentTemplate.designFile.split('/').pop()}
                    </Typography>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No design file selected
                    </Typography>
                  )}
                </Box>
                <Button
                  variant="outlined"
                  component="label"
                  fullWidth
                  startIcon={<CloudUploadIcon />}
                >
                  Upload Design File
                  <input
                    type="file"
                    name="designFile"
                    onChange={handleFileChange}
                    accept=".html,.css,.zip"
                    hidden
                  />
                </Button>
              </Grid>
            </Grid>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseModal}>Cancel</Button>
            <Button type="submit" variant="contained" color="primary">
              {currentTemplate ? 'Update' : 'Create'}
            </Button>
          </DialogActions>
        </form>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={openDeleteModal} onClose={() => setOpenDeleteModal(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the template "{templateToDelete?.name}"? This action cannot be undone.
            {templateToDelete?.purchaseCount > 0 && (
              <Typography color="error" sx={{ mt: 2 }}>
                Warning: This template has been purchased by {templateToDelete.purchaseCount} users.
                Deleting it will make it unavailable for them.
              </Typography>
            )}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDeleteModal(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </EnhancedAdminLayout>
  );
}
