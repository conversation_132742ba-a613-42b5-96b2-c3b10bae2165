/**
 * Project Structure Analysis Script
 * 
 * This script analyzes the project structure to identify:
 * - Duplicate files
 * - Unused files
 * - Inconsistent naming patterns
 * - Potential structural issues
 * 
 * To run this script:
 * node check-project-structure.js
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Configuration
const config = {
  projectDirs: [
    'vaivahik-nextjs',
    'vaivahik-backend'
  ],
  excludeDirs: [
    'node_modules',
    '.next',
    'build',
    'dist',
    'coverage',
    '.git'
  ],
  excludeFiles: [
    '.DS_Store',
    'package-lock.json',
    'yarn.lock'
  ],
  outputFile: 'project-structure-analysis.json',
  checkDuplicates: true,
  checkNaming: true,
  checkUnused: true
};

// Store file information
const fileRegistry = [];
const duplicates = [];
const namingIssues = [];
const potentialUnused = [];

/**
 * Calculate file hash
 * @param {string} filePath - Path to the file
 * @returns {string} MD5 hash of the file
 */
function calculateFileHash(filePath) {
  const fileBuffer = fs.readFileSync(filePath);
  const hashSum = crypto.createHash('md5');
  hashSum.update(fileBuffer);
  return hashSum.digest('hex');
}

/**
 * Check if a directory should be excluded
 * @param {string} dirPath - Directory path
 * @returns {boolean} True if the directory should be excluded
 */
function shouldExcludeDir(dirPath) {
  const dirName = path.basename(dirPath);
  return config.excludeDirs.includes(dirName);
}

/**
 * Check if a file should be excluded
 * @param {string} filePath - File path
 * @returns {boolean} True if the file should be excluded
 */
function shouldExcludeFile(filePath) {
  const fileName = path.basename(filePath);
  return config.excludeFiles.includes(fileName);
}

/**
 * Check naming consistency
 * @param {string} filePath - File path
 * @returns {Object|null} Naming issue or null if no issues
 */
function checkNamingConsistency(filePath) {
  const fileName = path.basename(filePath);
  const dirName = path.basename(path.dirname(filePath));
  
  // Check for mixed case in directory names
  if (dirName !== dirName.toLowerCase() && dirName !== 'VaivahikAI' && !dirName.match(/^[A-Z][a-z]+$/)) {
    return {
      type: 'directory',
      path: path.dirname(filePath),
      issue: 'Mixed case in directory name',
      recommendation: `Rename to ${dirName.toLowerCase()}`
    };
  }
  
  // Check for inconsistent file naming
  if (filePath.endsWith('.js') || filePath.endsWith('.jsx')) {
    // React components should be PascalCase
    if (filePath.includes('/components/') && !fileName.match(/^[A-Z][a-zA-Z0-9]*\.(js|jsx)$/)) {
      return {
        type: 'file',
        path: filePath,
        issue: 'Component file not in PascalCase',
        recommendation: `Rename to ${fileName.charAt(0).toUpperCase() + fileName.slice(1)}`
      };
    }
    
    // Pages should be kebab-case
    if (filePath.includes('/pages/') && fileName.includes('_') && !fileName.startsWith('_')) {
      return {
        type: 'file',
        path: filePath,
        issue: 'Page file uses underscore instead of hyphen',
        recommendation: `Rename to ${fileName.replace(/_/g, '-')}`
      };
    }
  }
  
  return null;
}

/**
 * Scan directory recursively
 * @param {string} dir - Directory to scan
 */
function scanDirectory(dir) {
  if (shouldExcludeDir(dir)) {
    return;
  }
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const itemPath = path.join(dir, item);
    const stats = fs.statSync(itemPath);
    
    if (stats.isDirectory()) {
      scanDirectory(itemPath);
    } else if (stats.isFile() && !shouldExcludeFile(itemPath)) {
      // Register file
      const fileInfo = {
        path: itemPath,
        size: stats.size,
        extension: path.extname(itemPath),
        lastModified: stats.mtime
      };
      
      // Calculate hash for duplicate detection
      if (config.checkDuplicates && stats.size > 0) {
        fileInfo.hash = calculateFileHash(itemPath);
      }
      
      fileRegistry.push(fileInfo);
      
      // Check naming consistency
      if (config.checkNaming) {
        const namingIssue = checkNamingConsistency(itemPath);
        if (namingIssue) {
          namingIssues.push(namingIssue);
        }
      }
    }
  }
}

/**
 * Find duplicate files
 */
function findDuplicates() {
  // Group files by hash
  const filesByHash = {};
  
  for (const file of fileRegistry) {
    if (file.hash) {
      if (!filesByHash[file.hash]) {
        filesByHash[file.hash] = [];
      }
      filesByHash[file.hash].push(file);
    }
  }
  
  // Find groups with more than one file
  for (const hash in filesByHash) {
    if (filesByHash[hash].length > 1) {
      duplicates.push({
        hash,
        files: filesByHash[hash].map(f => f.path),
        size: filesByHash[hash][0].size
      });
    }
  }
}

/**
 * Find potentially unused files
 */
function findUnusedFiles() {
  // This is a simplified approach - a more comprehensive approach would involve
  // parsing imports/requires in code files
  
  // Check for files that haven't been modified in a long time
  const now = new Date();
  const sixMonthsAgo = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
  
  for (const file of fileRegistry) {
    if (file.lastModified < sixMonthsAgo) {
      // Skip common files that might not be modified often
      if (file.path.includes('LICENSE') || 
          file.path.includes('README') || 
          file.path.endsWith('.gitignore')) {
        continue;
      }
      
      potentialUnused.push({
        path: file.path,
        lastModified: file.lastModified,
        daysSinceModified: Math.floor((now - file.lastModified) / (24 * 60 * 60 * 1000))
      });
    }
  }
}

/**
 * Run the analysis
 */
function runAnalysis() {
  console.log('Starting project structure analysis...');
  
  // Scan all project directories
  for (const dir of config.projectDirs) {
    if (fs.existsSync(dir)) {
      console.log(`Scanning ${dir}...`);
      scanDirectory(dir);
    } else {
      console.warn(`Directory not found: ${dir}`);
    }
  }
  
  console.log(`Found ${fileRegistry.length} files.`);
  
  // Find duplicates
  if (config.checkDuplicates) {
    console.log('Checking for duplicate files...');
    findDuplicates();
    console.log(`Found ${duplicates.length} duplicate file groups.`);
  }
  
  // Check for unused files
  if (config.checkUnused) {
    console.log('Checking for potentially unused files...');
    findUnusedFiles();
    console.log(`Found ${potentialUnused.length} potentially unused files.`);
  }
  
  // Prepare results
  const results = {
    timestamp: new Date().toISOString(),
    totalFiles: fileRegistry.length,
    filesByExtension: {},
    duplicates,
    namingIssues,
    potentialUnused
  };
  
  // Count files by extension
  for (const file of fileRegistry) {
    const ext = file.extension || 'no-extension';
    if (!results.filesByExtension[ext]) {
      results.filesByExtension[ext] = 0;
    }
    results.filesByExtension[ext]++;
  }
  
  // Write results to file
  fs.writeFileSync(
    config.outputFile,
    JSON.stringify(results, null, 2)
  );
  
  console.log(`Analysis complete. Results saved to ${config.outputFile}`);
  
  // Print summary
  console.log('\n=== Analysis Summary ===');
  console.log(`Total files: ${results.totalFiles}`);
  console.log(`Duplicate file groups: ${results.duplicates.length}`);
  console.log(`Naming issues: ${results.namingIssues.length}`);
  console.log(`Potentially unused files: ${results.potentialUnused.length}`);
  
  // Print file extension breakdown
  console.log('\nFiles by extension:');
  for (const ext in results.filesByExtension) {
    console.log(`  ${ext}: ${results.filesByExtension[ext]}`);
  }
  
  return results;
}

// Run the analysis if this file is executed directly
if (require.main === module) {
  runAnalysis();
}

module.exports = {
  runAnalysis
};
