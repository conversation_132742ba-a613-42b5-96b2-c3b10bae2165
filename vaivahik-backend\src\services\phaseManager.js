/**
 * Phase Manager Service
 * Handles switching between different matching algorithm phases
 */

const { PrismaClient } = require('@prisma/client');
const FlexibilityService = require('./flexibilityService');

class PhaseManager {
  constructor() {
    this.prisma = new PrismaClient();
    this.flexibilityService = new FlexibilityService();
    
    this.phases = {
      'v1.0': {
        name: 'Current Rule-Based',
        description: 'Traditional rule-based matching with fixed preferences',
        features: ['Basic compatibility scoring', 'Fixed preference weights', 'Simple filtering'],
        status: 'ACTIVE',
        implementation: 'COMPLETE'
      },
      'v1.5': {
        name: 'Flexible Matching',
        description: 'Enhanced matching with flexible preferences and compatibility groups',
        features: ['Flexible age ranges', 'Religion compatibility groups', 'Caste flexibility', 'Gradual scoring'],
        status: 'READY',
        implementation: 'COMPLETE'
      },
      'v2.0': {
        name: 'Personalized AI',
        description: 'AI-powered personalization based on user behavior',
        features: ['Behavioral learning', 'Dynamic preferences', 'User adaptation', 'Interaction analysis'],
        status: 'PLANNED',
        implementation: 'PENDING'
      },
      'v2.5': {
        name: 'Intelligent Features',
        description: 'Advanced AI features for enhanced user experience',
        features: ['Smart explanations', 'Predictive scoring', 'AI recommendations', 'Conversation analysis'],
        status: 'PLANNED',
        implementation: 'PENDING'
      },
      'v3.0': {
        name: 'Advanced AI',
        description: 'Cutting-edge AI algorithms for superior matching',
        features: ['Multi-modal learning', 'Graph networks', 'Reinforcement learning', 'Real-time adaptation'],
        status: 'PLANNED',
        implementation: 'PENDING'
      }
    };
  }

  /**
   * Get all available phases
   */
  async getAvailablePhases() {
    return Object.entries(this.phases).map(([version, phase]) => ({
      version,
      ...phase,
      isImplemented: phase.implementation === 'COMPLETE',
      canActivate: phase.implementation === 'COMPLETE'
    }));
  }

  /**
   * Get current active phase
   */
  async getCurrentPhase() {
    try {
      const settings = await this.prisma.systemSettings.findFirst({
        where: { key: 'matching_algorithm_version' }
      });
      
      const currentVersion = settings?.value || 'v1.0';
      return {
        version: currentVersion,
        ...this.phases[currentVersion]
      };
    } catch (error) {
      console.error('Error getting current phase:', error);
      return {
        version: 'v1.0',
        ...this.phases['v1.0']
      };
    }
  }

  /**
   * Switch to a different phase
   */
  async switchPhase(targetVersion, adminUserId) {
    try {
      // Validate target version
      if (!this.phases[targetVersion]) {
        throw new Error(`Invalid phase version: ${targetVersion}`);
      }

      // Check if phase is implemented
      if (this.phases[targetVersion].implementation !== 'COMPLETE') {
        throw new Error(`Phase ${targetVersion} is not yet implemented`);
      }

      // Get current phase
      const currentPhase = await this.getCurrentPhase();
      
      if (currentPhase.version === targetVersion) {
        return {
          success: true,
          message: `Already using phase ${targetVersion}`,
          currentPhase: currentPhase
        };
      }

      // Perform phase switch
      await this.performPhaseSwitch(currentPhase.version, targetVersion, adminUserId);

      // Update system settings
      await this.prisma.systemSettings.upsert({
        where: { key: 'matching_algorithm_version' },
        update: { 
          value: targetVersion,
          updatedAt: new Date(),
          updatedBy: adminUserId
        },
        create: {
          key: 'matching_algorithm_version',
          value: targetVersion,
          createdBy: adminUserId
        }
      });

      // Log phase change
      await this.logPhaseChange(currentPhase.version, targetVersion, adminUserId);

      return {
        success: true,
        message: `Successfully switched from ${currentPhase.version} to ${targetVersion}`,
        previousPhase: currentPhase,
        newPhase: {
          version: targetVersion,
          ...this.phases[targetVersion]
        }
      };

    } catch (error) {
      console.error('Error switching phase:', error);
      throw error;
    }
  }

  /**
   * Perform the actual phase switch operations
   */
  async performPhaseSwitch(fromVersion, toVersion, adminUserId) {
    console.log(`Switching from ${fromVersion} to ${toVersion}`);

    // Phase-specific switch logic
    switch (toVersion) {
      case 'v1.5':
        await this.enableFlexibilityFeatures();
        break;
      case 'v2.0':
        await this.enablePersonalizationFeatures();
        break;
      case 'v2.5':
        await this.enableIntelligentFeatures();
        break;
      case 'v3.0':
        await this.enableAdvancedAIFeatures();
        break;
      default:
        // v1.0 or unknown - disable all advanced features
        await this.disableAdvancedFeatures();
    }

    // Clear caches that might be affected by the phase change
    await this.clearRelevantCaches(toVersion);
  }

  /**
   * Enable flexibility features for Phase 1
   */
  async enableFlexibilityFeatures() {
    console.log('Enabling flexibility features...');
    
    // Update system configuration
    await this.prisma.systemSettings.upsert({
      where: { key: 'flexibility_enabled' },
      update: { value: 'true' },
      create: { key: 'flexibility_enabled', value: 'true' }
    });

    // Initialize default flexibility settings for existing users
    const usersWithoutFlexibility = await this.prisma.user.findMany({
      where: {
        flexibilitySettings: null
      },
      select: { id: true }
    });

    for (const user of usersWithoutFlexibility) {
      await this.flexibilityService.createDefaultSettings(user.id);
    }

    console.log(`Initialized flexibility settings for ${usersWithoutFlexibility.length} users`);
  }

  /**
   * Enable personalization features for Phase 2
   */
  async enablePersonalizationFeatures() {
    console.log('Enabling personalization features...');
    
    await this.prisma.systemSettings.upsert({
      where: { key: 'personalization_enabled' },
      update: { value: 'true' },
      create: { key: 'personalization_enabled', value: 'true' }
    });

    // Initialize behavioral tracking
    await this.initializeBehavioralTracking();
  }

  /**
   * Enable intelligent features for Phase 3
   */
  async enableIntelligentFeatures() {
    console.log('Enabling intelligent features...');
    
    await this.prisma.systemSettings.upsert({
      where: { key: 'intelligent_features_enabled' },
      update: { value: 'true' },
      create: { key: 'intelligent_features_enabled', value: 'true' }
    });
  }

  /**
   * Enable advanced AI features for Phase 4
   */
  async enableAdvancedAIFeatures() {
    console.log('Enabling advanced AI features...');
    
    await this.prisma.systemSettings.upsert({
      where: { key: 'advanced_ai_enabled' },
      update: { value: 'true' },
      create: { key: 'advanced_ai_enabled', value: 'true' }
    });
  }

  /**
   * Disable all advanced features (revert to v1.0)
   */
  async disableAdvancedFeatures() {
    console.log('Disabling advanced features...');
    
    const featureKeys = [
      'flexibility_enabled',
      'personalization_enabled', 
      'intelligent_features_enabled',
      'advanced_ai_enabled'
    ];

    for (const key of featureKeys) {
      await this.prisma.systemSettings.upsert({
        where: { key },
        update: { value: 'false' },
        create: { key, value: 'false' }
      });
    }
  }

  /**
   * Clear caches affected by phase change
   */
  async clearRelevantCaches(newVersion) {
    console.log(`Clearing caches for phase ${newVersion}...`);
    
    // Clear compatibility scores cache
    await this.prisma.compatibilityScores.deleteMany({});
    
    // Clear user embeddings cache if switching to/from AI phases
    if (['v2.0', 'v2.5', 'v3.0'].includes(newVersion)) {
      // Clear ML-related caches
      console.log('Clearing ML caches...');
    }
  }

  /**
   * Log phase change for audit trail
   */
  async logPhaseChange(fromVersion, toVersion, adminUserId) {
    await this.prisma.systemAuditLog.create({
      data: {
        action: 'PHASE_CHANGE',
        details: {
          fromVersion,
          toVersion,
          timestamp: new Date(),
          adminUserId
        },
        performedBy: adminUserId,
        timestamp: new Date()
      }
    });
  }

  /**
   * Initialize behavioral tracking for personalization
   */
  async initializeBehavioralTracking() {
    // Create behavioral tracking tables if they don't exist
    // This would typically be done through migrations
    console.log('Initializing behavioral tracking...');
  }

  /**
   * Get phase-specific configuration
   */
  async getPhaseConfiguration(version) {
    const phase = this.phases[version];
    if (!phase) {
      throw new Error(`Unknown phase version: ${version}`);
    }

    // Get phase-specific settings from database
    const settings = await this.prisma.systemSettings.findMany({
      where: {
        key: {
          startsWith: version.replace('.', '_') + '_'
        }
      }
    });

    const config = {};
    settings.forEach(setting => {
      config[setting.key] = setting.value;
    });

    return {
      phase,
      configuration: config
    };
  }

  /**
   * Validate phase readiness
   */
  async validatePhaseReadiness(version) {
    const phase = this.phases[version];
    
    const readiness = {
      isImplemented: phase.implementation === 'COMPLETE',
      hasRequiredData: true,
      hasRequiredInfrastructure: true,
      estimatedUsers: 0,
      warnings: [],
      errors: []
    };

    // Check user count for personalization phases
    if (['v2.0', 'v2.5', 'v3.0'].includes(version)) {
      const userCount = await this.prisma.user.count();
      readiness.estimatedUsers = userCount;
      
      if (userCount < 1000) {
        readiness.warnings.push('Personalization works better with more users (recommended: 1000+)');
      }
    }

    // Check for required infrastructure
    if (version === 'v3.0') {
      readiness.warnings.push('Advanced AI features require additional GPU resources');
    }

    return readiness;
  }
}

module.exports = PhaseManager;
