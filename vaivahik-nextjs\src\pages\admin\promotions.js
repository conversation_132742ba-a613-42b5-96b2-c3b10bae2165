import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Switch,
  Tab,
  Tabs,
  TextField,
  Typography,
  Alert,
  AlertTitle
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ActivateIcon from '@mui/icons-material/PlayArrow';
import DeactivateIcon from '@mui/icons-material/Stop';
import { DataGrid } from '@mui/x-data-grid';
import { toast, ToastContainer } from 'react-toastify';

export default function PromotionsPage() {
  const [promotions, setPromotions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedPromotion, setSelectedPromotion] = useState(null);
  const [showPromotionModal, setShowPromotionModal] = useState(false);
  const [modalMode, setModalMode] = useState('view'); // 'view', 'edit', 'create'
  const [formData, setFormData] = useState({
    name: '',
    durationDays: 7,
    featureOverrides: {
      BASIC: {
        dailyMessageLimit: 20,
        canStartNewConversations: true,
        canSendImages: false,
        messageRetentionDays: 30
      },
      VERIFIED: {
        dailyMessageLimit: 50,
        canStartNewConversations: true,
        canSendImages: true,
        messageRetentionDays: 60
      }
    },
    moderationOverrides: {
      BASIC: {
        strictness: 'medium', // 'low', 'medium', 'high'
        autoReject: false,
        maskProfanity: true,
        allowContactInfo: false,
        allowedContactTypes: []
      },
      VERIFIED: {
        strictness: 'low',
        autoReject: false,
        maskProfanity: true,
        allowContactInfo: true,
        allowedContactTypes: ['email']
      }
    },
    display: {
      title: '',
      description: '',
      bannerColor: '#FF5722',
      bannerTextColor: '#FFFFFF',
      showCountdown: true,
      showOnHomepage: true,
      showInApp: true
    }
  });
  const [showActivateModal, setShowActivateModal] = useState(false);
  const [activateDuration, setActivateDuration] = useState(7);
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  const router = useRouter();

  // Fetch promotions on component mount
  useEffect(() => {
    fetchPromotions();
  }, []);

  // Generate mock promotions data
  const generateMockPromotions = () => {
    return [
      {
        name: 'freeChatWeek',
        isActive: false,
        startDate: null,
        endDate: null,
        durationDays: 7,
        isCurrentlyActive: false,
        featureOverrides: {
          BASIC: {
            dailyMessageLimit: 20,
            canStartNewConversations: true,
            canSendImages: false,
            messageRetentionDays: 30
          },
          VERIFIED: {
            dailyMessageLimit: 50,
            canStartNewConversations: true,
            canSendImages: true,
            messageRetentionDays: 60
          }
        },
        moderationOverrides: {
          BASIC: {
            strictness: 'medium',
            autoReject: false,
            maskProfanity: true,
            allowContactInfo: false,
            allowedContactTypes: []
          },
          VERIFIED: {
            strictness: 'low',
            autoReject: false,
            maskProfanity: true,
            allowContactInfo: true,
            allowedContactTypes: ['email']
          }
        },
        display: {
          title: "Free Chat Week!",
          description: "Enjoy unlimited messaging for a limited time!",
          bannerColor: "#FF5722",
          bannerTextColor: "#FFFFFF",
          showCountdown: true,
          showOnHomepage: true,
          showInApp: true
        }
      },
      {
        name: 'newUserBonus',
        isActive: true,
        startDate: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
        endDate: new Date(Date.now() + 86400000 * 5).toISOString(), // 5 days from now
        durationDays: 7,
        isCurrentlyActive: true,
        featureOverrides: {
          BASIC: {
            dailyMessageLimit: 30,
            canStartNewConversations: true,
            canSendImages: true,
            messageRetentionDays: 45
          },
          VERIFIED: {
            dailyMessageLimit: 100,
            canStartNewConversations: true,
            canSendImages: true,
            messageRetentionDays: 90
          }
        },
        moderationOverrides: {
          BASIC: {
            strictness: 'low',
            autoReject: false,
            maskProfanity: true,
            allowContactInfo: true,
            allowedContactTypes: ['email']
          },
          VERIFIED: {
            strictness: 'low',
            autoReject: false,
            maskProfanity: false,
            allowContactInfo: true,
            allowedContactTypes: ['email', 'phone']
          }
        },
        display: {
          title: "Welcome Bonus!",
          description: "As a new user, enjoy enhanced messaging features!",
          bannerColor: "#4CAF50",
          bannerTextColor: "#FFFFFF",
          showCountdown: false,
          showOnHomepage: true,
          showInApp: true
        }
      },
      {
        name: 'festivalPromotion',
        isActive: false,
        startDate: null,
        endDate: null,
        durationDays: 3,
        isCurrentlyActive: false,
        featureOverrides: {
          BASIC: {
            dailyMessageLimit: 25,
            canStartNewConversations: true,
            canSendImages: true,
            messageRetentionDays: 45
          },
          VERIFIED: {
            dailyMessageLimit: 75,
            canStartNewConversations: true,
            canSendImages: true,
            messageRetentionDays: 90
          }
        },
        moderationOverrides: {
          BASIC: {
            strictness: 'medium',
            autoReject: false,
            maskProfanity: true
          },
          VERIFIED: {
            strictness: 'low',
            autoReject: false,
            maskProfanity: false
          }
        },
        display: {
          title: "Festival Special!",
          description: "Celebrate with special messaging features!",
          bannerColor: "#9C27B0",
          bannerTextColor: "#FFFFFF",
          showCountdown: true,
          showOnHomepage: true,
          showInApp: true
        }
      }
    ];
  };

  // Fetch promotions from API
  const fetchPromotions = async () => {
    try {
      setLoading(true);

      // Get token from localStorage
      const token = localStorage.getItem('adminAccessToken');

      if (!token) {
        router.push('/admin/login');
        return;
      }

      try {
        const response = await fetch(`/api/admin/promotions`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (!response.ok) {
          console.warn(`API returned status: ${response.status}. Using mock data instead.`);
          throw new Error(`Failed to fetch promotions: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          setPromotions(data.promotions);
        } else {
          console.warn('API returned error:', data.message);
          throw new Error(data.message || 'Failed to fetch promotions');
        }
      } catch (apiError) {
        console.warn('Using mock data as fallback due to API error:', apiError.message);
        // Use mock data when API fails
        const mockData = generateMockPromotions();
        setPromotions(mockData);
        toast.info('Using mock data for promotions. Connect to backend for real data.');
      }
    } catch (error) {
      console.error('Error in fetchPromotions:', error);
      // Fallback to mock data if any other error occurs
      const mockData = generateMockPromotions();
      setPromotions(mockData);
      toast.info('Using mock data for promotions. Connect to backend for real data.');
    } finally {
      setLoading(false);
    }
  };

  // Handle promotion activation
  const handleActivatePromotion = async () => {
    try {
      // Get token from localStorage
      const token = localStorage.getItem('adminAccessToken');

      if (!token) {
        router.push('/admin/login');
        return;
      }

      try {
        const response = await fetch(`/api/admin/promotions/${selectedPromotion.name}/activate`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            durationDays: activateDuration
          })
        });

        if (!response.ok) {
          console.warn(`API returned status: ${response.status}. Using mock data instead.`);
          throw new Error(`Failed to activate promotion: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          toast.success(`Promotion "${selectedPromotion.name}" activated successfully`);
          setShowActivateModal(false);
          fetchPromotions();
        } else {
          console.warn('API returned error:', data.message);
          throw new Error(data.message || 'Failed to activate promotion');
        }
      } catch (apiError) {
        console.warn('Using mock data as fallback due to API error:', apiError.message);
        // Simulate successful activation with mock data
        toast.success(`Promotion "${selectedPromotion.name}" activated successfully (mock)`);
        setShowActivateModal(false);

        // Update the promotions list with the activated promotion
        const updatedPromotions = promotions.map(promo => {
          if (promo.name === selectedPromotion.name) {
            const now = new Date();
            const endDate = new Date(now);
            endDate.setDate(now.getDate() + activateDuration);

            return {
              ...promo,
              isActive: true,
              isCurrentlyActive: true,
              startDate: now.toISOString(),
              endDate: endDate.toISOString()
            };
          }
          return promo;
        });

        setPromotions(updatedPromotions);
      }
    } catch (error) {
      console.error('Error in handleActivatePromotion:', error);
      toast.error(error.message || 'Failed to activate promotion');
    }
  };

  // Handle promotion deactivation
  const handleDeactivatePromotion = async () => {
    try {
      // Get token from localStorage
      const token = localStorage.getItem('adminAccessToken');

      if (!token) {
        router.push('/admin/login');
        return;
      }

      try {
        const response = await fetch(`/api/admin/promotions/${selectedPromotion.name}/deactivate`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          console.warn(`API returned status: ${response.status}. Using mock data instead.`);
          throw new Error(`Failed to deactivate promotion: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          toast.success(`Promotion "${selectedPromotion.name}" deactivated successfully`);
          setShowDeactivateModal(false);
          fetchPromotions();
        } else {
          console.warn('API returned error:', data.message);
          throw new Error(data.message || 'Failed to deactivate promotion');
        }
      } catch (apiError) {
        console.warn('Using mock data as fallback due to API error:', apiError.message);
        // Simulate successful deactivation with mock data
        toast.success(`Promotion "${selectedPromotion.name}" deactivated successfully (mock)`);
        setShowDeactivateModal(false);

        // Update the promotions list with the deactivated promotion
        const updatedPromotions = promotions.map(promo => {
          if (promo.name === selectedPromotion.name) {
            return {
              ...promo,
              isActive: false,
              isCurrentlyActive: false
            };
          }
          return promo;
        });

        setPromotions(updatedPromotions);
      }
    } catch (error) {
      console.error('Error in handleDeactivatePromotion:', error);
      toast.error(error.message || 'Failed to deactivate promotion');
    }
  };

  // Handle form submission
  const handleSubmitForm = async () => {
    try {
      // Validate form data
      if (!formData.name.trim()) {
        toast.error('Promotion name is required');
        return;
      }

      if (!formData.durationDays || formData.durationDays < 1) {
        toast.error('Duration must be at least 1 day');
        return;
      }

      // Ensure all required objects have the correct structure
      const validatedFormData = {
        name: formData.name.trim(),
        durationDays: parseInt(formData.durationDays, 10),
        featureOverrides: ensureFeatureOverrides(formData.featureOverrides),
        moderationOverrides: ensureModerationOverrides(formData.moderationOverrides),
        display: ensureDisplaySettings(formData.display)
      };

      // Get token from localStorage
      const token = localStorage.getItem('adminAccessToken');

      if (!token) {
        router.push('/admin/login');
        return;
      }

      let url = `/api/admin/promotions`;
      let method = 'POST';

      if (modalMode === 'edit') {
        url = `/api/admin/promotions/${selectedPromotion.name}`;
        method = 'PUT';
      }

      try {
        const response = await fetch(url, {
          method,
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(validatedFormData)
        });

        if (!response.ok) {
          console.warn(`API returned status: ${response.status}. Using mock data instead.`);
          throw new Error(`Failed to ${modalMode === 'edit' ? 'update' : 'create'} promotion: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          toast.success(`Promotion ${modalMode === 'edit' ? 'updated' : 'created'} successfully`);
          setShowPromotionModal(false);
          fetchPromotions();
        } else {
          console.warn('API returned error:', data.message);
          throw new Error(data.message || `Failed to ${modalMode === 'edit' ? 'update' : 'create'} promotion`);
        }
      } catch (apiError) {
        console.warn('Using mock data as fallback due to API error:', apiError.message);
        // Simulate successful form submission with mock data
        toast.success(`Promotion ${modalMode === 'edit' ? 'updated' : 'created'} successfully (mock)`);
        setShowPromotionModal(false);

        if (modalMode === 'edit') {
          // Update existing promotion in the list
          const updatedPromotions = promotions.map(promo => {
            if (promo.name === selectedPromotion.name) {
              return {
                ...promo,
                durationDays: validatedFormData.durationDays,
                featureOverrides: validatedFormData.featureOverrides,
                moderationOverrides: validatedFormData.moderationOverrides,
                display: validatedFormData.display
              };
            }
            return promo;
          });

          setPromotions(updatedPromotions);
        } else {
          // Add new promotion to the list
          const newPromotion = {
            name: validatedFormData.name,
            isActive: false,
            startDate: null,
            endDate: null,
            durationDays: validatedFormData.durationDays,
            isCurrentlyActive: false,
            featureOverrides: validatedFormData.featureOverrides,
            moderationOverrides: validatedFormData.moderationOverrides,
            display: validatedFormData.display
          };

          setPromotions([...promotions, newPromotion]);
        }
      }
    } catch (error) {
      console.error(`Error in handleSubmitForm (${modalMode}):`, error);
      toast.error(error.message || `Failed to ${modalMode === 'edit' ? 'update' : 'create'} promotion`);
    }
  };

  // Handle form input change
  const handleInputChange = (e, section, subsection, field) => {
    const { name, value, type, checked } = e.target;

    try {
      if (section && subsection && field) {
        // Handle nested fields (featureOverrides, moderationOverrides)
        setFormData(prev => {
          // Ensure the section exists
          const sectionData = prev[section] ||
            (section === 'featureOverrides' ? defaultFeatureOverrides :
             section === 'moderationOverrides' ? defaultModerationOverrides : {});

          // Ensure the subsection exists
          const subsectionData = sectionData[subsection] || {};

          return {
            ...prev,
            [section]: {
              ...sectionData,
              [subsection]: {
                ...subsectionData,
                [field]: type === 'checkbox' ? checked : value
              }
            }
          };
        });
      } else if (section && field) {
        // Handle nested fields (display)
        setFormData(prev => {
          // Ensure the section exists
          const sectionData = prev[section] ||
            (section === 'display' ? defaultDisplaySettings : {});

          return {
            ...prev,
            [section]: {
              ...sectionData,
              [field]: type === 'checkbox' ? checked : value
            }
          };
        });
      } else {
        // Handle top-level fields
        setFormData(prev => ({
          ...prev,
          [name]: type === 'checkbox' ? checked : value
        }));
      }
    } catch (error) {
      console.error('Error updating form data:', error);
      toast.error('An error occurred while updating the form. Please try again.');
    }
  };

  // Handle tab change
  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
  };

  // Default feature overrides
  const defaultFeatureOverrides = {
    BASIC: {
      dailyMessageLimit: 20,
      canStartNewConversations: true,
      canSendImages: false,
      messageRetentionDays: 30
    },
    VERIFIED: {
      dailyMessageLimit: 50,
      canStartNewConversations: true,
      canSendImages: true,
      messageRetentionDays: 60
    }
  };

  // Default moderation overrides
  const defaultModerationOverrides = {
    BASIC: {
      strictness: 'medium',
      autoReject: false,
      maskProfanity: true,
      allowContactInfo: false,
      allowedContactTypes: []
    },
    VERIFIED: {
      strictness: 'low',
      autoReject: false,
      maskProfanity: true,
      allowContactInfo: true,
      allowedContactTypes: ['email']
    }
  };

  // Default display settings
  const defaultDisplaySettings = {
    title: '',
    description: '',
    bannerColor: '#FF5722',
    bannerTextColor: '#FFFFFF',
    showCountdown: true,
    showOnHomepage: true,
    showInApp: true
  };

  // Ensure feature overrides have the correct structure
  const ensureFeatureOverrides = (overrides) => {
    if (!overrides) return { ...defaultFeatureOverrides };

    return {
      BASIC: {
        dailyMessageLimit: overrides.BASIC?.dailyMessageLimit ?? defaultFeatureOverrides.BASIC.dailyMessageLimit,
        canStartNewConversations: overrides.BASIC?.canStartNewConversations ?? defaultFeatureOverrides.BASIC.canStartNewConversations,
        canSendImages: overrides.BASIC?.canSendImages ?? defaultFeatureOverrides.BASIC.canSendImages,
        messageRetentionDays: overrides.BASIC?.messageRetentionDays ?? defaultFeatureOverrides.BASIC.messageRetentionDays
      },
      VERIFIED: {
        dailyMessageLimit: overrides.VERIFIED?.dailyMessageLimit ?? defaultFeatureOverrides.VERIFIED.dailyMessageLimit,
        canStartNewConversations: overrides.VERIFIED?.canStartNewConversations ?? defaultFeatureOverrides.VERIFIED.canStartNewConversations,
        canSendImages: overrides.VERIFIED?.canSendImages ?? defaultFeatureOverrides.VERIFIED.canSendImages,
        messageRetentionDays: overrides.VERIFIED?.messageRetentionDays ?? defaultFeatureOverrides.VERIFIED.messageRetentionDays
      }
    };
  };

  // Ensure moderation overrides have the correct structure
  const ensureModerationOverrides = (overrides) => {
    if (!overrides) return { ...defaultModerationOverrides };

    return {
      BASIC: {
        strictness: overrides.BASIC?.strictness ?? defaultModerationOverrides.BASIC.strictness,
        autoReject: overrides.BASIC?.autoReject ?? defaultModerationOverrides.BASIC.autoReject,
        maskProfanity: overrides.BASIC?.maskProfanity ?? defaultModerationOverrides.BASIC.maskProfanity,
        allowContactInfo: overrides.BASIC?.allowContactInfo ?? defaultModerationOverrides.BASIC.allowContactInfo,
        allowedContactTypes: overrides.BASIC?.allowedContactTypes ?? defaultModerationOverrides.BASIC.allowedContactTypes
      },
      VERIFIED: {
        strictness: overrides.VERIFIED?.strictness ?? defaultModerationOverrides.VERIFIED.strictness,
        autoReject: overrides.VERIFIED?.autoReject ?? defaultModerationOverrides.VERIFIED.autoReject,
        maskProfanity: overrides.VERIFIED?.maskProfanity ?? defaultModerationOverrides.VERIFIED.maskProfanity,
        allowContactInfo: overrides.VERIFIED?.allowContactInfo ?? defaultModerationOverrides.VERIFIED.allowContactInfo,
        allowedContactTypes: overrides.VERIFIED?.allowedContactTypes ?? defaultModerationOverrides.VERIFIED.allowedContactTypes
      }
    };
  };

  // Ensure display settings have the correct structure
  const ensureDisplaySettings = (display) => {
    if (!display) return { ...defaultDisplaySettings };

    return {
      title: display.title ?? defaultDisplaySettings.title,
      description: display.description ?? defaultDisplaySettings.description,
      bannerColor: display.bannerColor ?? defaultDisplaySettings.bannerColor,
      bannerTextColor: display.bannerTextColor ?? defaultDisplaySettings.bannerTextColor,
      showCountdown: display.showCountdown ?? defaultDisplaySettings.showCountdown,
      showOnHomepage: display.showOnHomepage ?? defaultDisplaySettings.showOnHomepage,
      showInApp: display.showInApp ?? defaultDisplaySettings.showInApp
    };
  };

  // Open promotion modal in view mode
  const handleViewPromotion = (promotion) => {
    setSelectedPromotion(promotion);
    setFormData({
      name: promotion.name || '',
      durationDays: promotion.durationDays || 7,
      featureOverrides: ensureFeatureOverrides(promotion.featureOverrides),
      moderationOverrides: ensureModerationOverrides(promotion.moderationOverrides),
      display: ensureDisplaySettings(promotion.display)
    });
    setModalMode('view');
    setShowPromotionModal(true);
  };

  // Open promotion modal in edit mode
  const handleEditPromotion = (promotion) => {
    setSelectedPromotion(promotion);
    setFormData({
      name: promotion.name || '',
      durationDays: promotion.durationDays || 7,
      featureOverrides: ensureFeatureOverrides(promotion.featureOverrides),
      moderationOverrides: ensureModerationOverrides(promotion.moderationOverrides),
      display: ensureDisplaySettings(promotion.display)
    });
    setModalMode('edit');
    setShowPromotionModal(true);
  };

  // Open promotion modal in create mode
  const handleCreatePromotion = () => {
    setSelectedPromotion(null);
    setFormData({
      name: '',
      durationDays: 7,
      featureOverrides: { ...defaultFeatureOverrides },
      moderationOverrides: { ...defaultModerationOverrides },
      display: { ...defaultDisplaySettings }
    });
    setModalMode('create');
    setShowPromotionModal(true);
  };

  // Open activate modal
  const handleOpenActivateModal = (promotion) => {
    setSelectedPromotion(promotion);
    setActivateDuration(promotion.durationDays || 7);
    setShowActivateModal(true);
  };

  // Open deactivate modal
  const handleOpenDeactivateModal = (promotion) => {
    setSelectedPromotion(promotion);
    setShowDeactivateModal(true);
  };

  // DataGrid columns
  const columns = [
    { field: 'name', headerName: 'Name', flex: 1 },
    {
      field: 'isCurrentlyActive',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.row.isCurrentlyActive ? 'Active' : 'Inactive'}
          color={params.row.isCurrentlyActive ? 'success' : 'default'}
          size="small"
        />
      )
    },
    {
      field: 'startDate',
      headerName: 'Start Date',
      width: 180,
      valueFormatter: (params) => {
        if (!params || params.value === null || params.value === undefined) return 'Not started';
        return new Date(params.value).toLocaleString();
      }
    },
    {
      field: 'endDate',
      headerName: 'End Date',
      width: 180,
      valueFormatter: (params) => {
        if (!params || params.value === null || params.value === undefined) return 'Not set';
        return new Date(params.value).toLocaleString();
      }
    },
    {
      field: 'durationDays',
      headerName: 'Duration (Days)',
      width: 150,
      valueFormatter: (params) => {
        if (!params || params.value === null || params.value === undefined) return 'Not set';
        return params.value;
      }
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 200,
      sortable: false,
      renderCell: (params) => (
        <Box>
          <IconButton
            color="primary"
            onClick={() => handleViewPromotion(params.row)}
            title="View"
          >
            <EditIcon />
          </IconButton>

          {params.row.isCurrentlyActive ? (
            <IconButton
              color="error"
              onClick={() => handleOpenDeactivateModal(params.row)}
              title="Deactivate"
            >
              <DeactivateIcon />
            </IconButton>
          ) : (
            <IconButton
              color="success"
              onClick={() => handleOpenActivateModal(params.row)}
              title="Activate"
            >
              <ActivateIcon />
            </IconButton>
          )}
        </Box>
      )
    }
  ];

  return (
    <EnhancedAdminLayout>
      <ToastContainer position="top-right" autoClose={5000} />
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4">Promotions Management</Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreatePromotion}
          >
            Create Promotion
          </Button>
        </Box>

        <Paper sx={{ height: 400, width: '100%' }}>
          <DataGrid
            rows={promotions}
            columns={columns}
            pageSize={5}
            rowsPerPageOptions={[5, 10, 20]}
            loading={loading}
            getRowId={(row) => row.name}
            disableSelectionOnClick
          />
        </Paper>

        {/* Promotion Modal */}
        <Dialog
          open={showPromotionModal}
          onClose={() => setShowPromotionModal(false)}
          maxWidth="md"
          fullWidth
        >
          <DialogTitle>
            {modalMode === 'view' ? 'View Promotion' :
             modalMode === 'edit' ? 'Edit Promotion' : 'Create Promotion'}
          </DialogTitle>

          <DialogContent>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
              <Tabs value={activeTab} onChange={handleTabChange}>
                <Tab label="Basic Info" />
                <Tab label="Feature Overrides" />
                <Tab label="Moderation Overrides" />
                <Tab label="Display Settings" />
              </Tabs>
            </Box>

            {/* Basic Info Tab */}
            {activeTab === 0 && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    label="Promotion Name"
                    name="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange(e)}
                    fullWidth
                    margin="normal"
                    disabled={modalMode === 'view' || modalMode === 'edit'}
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    label="Duration (Days)"
                    name="durationDays"
                    type="number"
                    value={formData.durationDays}
                    onChange={(e) => handleInputChange(e)}
                    fullWidth
                    margin="normal"
                    disabled={modalMode === 'view'}
                    required
                    InputProps={{ inputProps: { min: 1 } }}
                  />
                </Grid>

                {selectedPromotion && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="Start Date"
                        value={selectedPromotion.startDate ? new Date(selectedPromotion.startDate).toLocaleString() : 'Not started'}
                        fullWidth
                        margin="normal"
                        disabled
                      />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <TextField
                        label="End Date"
                        value={selectedPromotion.endDate ? new Date(selectedPromotion.endDate).toLocaleString() : 'Not set'}
                        fullWidth
                        margin="normal"
                        disabled
                      />
                    </Grid>

                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Switch
                            checked={selectedPromotion.isCurrentlyActive}
                            disabled
                          />
                        }
                        label={selectedPromotion.isCurrentlyActive ? 'Active' : 'Inactive'}
                      />
                    </Grid>
                  </>
                )}
              </Grid>
            )}

            {/* Feature Overrides Tab */}
            {activeTab === 1 && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Card variant="outlined">
                    <CardHeader title="BASIC Tier Overrides" />
                    <Divider />
                    <CardContent>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <TextField
                            label="Daily Message Limit"
                            type="number"
                            value={formData.featureOverrides.BASIC.dailyMessageLimit}
                            onChange={(e) => handleInputChange(e, 'featureOverrides', 'BASIC', 'dailyMessageLimit')}
                            fullWidth
                            margin="normal"
                            disabled={modalMode === 'view'}
                            InputProps={{ inputProps: { min: 0 } }}
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.featureOverrides.BASIC.canStartNewConversations}
                                onChange={(e) => handleInputChange(e, 'featureOverrides', 'BASIC', 'canStartNewConversations')}
                                disabled={modalMode === 'view'}
                              />
                            }
                            label="Can Start New Conversations"
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.featureOverrides.BASIC.canSendImages}
                                onChange={(e) => handleInputChange(e, 'featureOverrides', 'BASIC', 'canSendImages')}
                                disabled={modalMode === 'view'}
                              />
                            }
                            label="Can Send Images"
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <TextField
                            label="Message Retention (Days)"
                            type="number"
                            value={formData.featureOverrides.BASIC.messageRetentionDays}
                            onChange={(e) => handleInputChange(e, 'featureOverrides', 'BASIC', 'messageRetentionDays')}
                            fullWidth
                            margin="normal"
                            disabled={modalMode === 'view'}
                            InputProps={{ inputProps: { min: 1 } }}
                          />
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Card variant="outlined">
                    <CardHeader title="VERIFIED Tier Overrides" />
                    <Divider />
                    <CardContent>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <TextField
                            label="Daily Message Limit"
                            type="number"
                            value={formData.featureOverrides.VERIFIED.dailyMessageLimit}
                            onChange={(e) => handleInputChange(e, 'featureOverrides', 'VERIFIED', 'dailyMessageLimit')}
                            fullWidth
                            margin="normal"
                            disabled={modalMode === 'view'}
                            InputProps={{ inputProps: { min: 0 } }}
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.featureOverrides.VERIFIED.canStartNewConversations}
                                onChange={(e) => handleInputChange(e, 'featureOverrides', 'VERIFIED', 'canStartNewConversations')}
                                disabled={modalMode === 'view'}
                              />
                            }
                            label="Can Start New Conversations"
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.featureOverrides.VERIFIED.canSendImages}
                                onChange={(e) => handleInputChange(e, 'featureOverrides', 'VERIFIED', 'canSendImages')}
                                disabled={modalMode === 'view'}
                              />
                            }
                            label="Can Send Images"
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <TextField
                            label="Message Retention (Days)"
                            type="number"
                            value={formData.featureOverrides.VERIFIED.messageRetentionDays}
                            onChange={(e) => handleInputChange(e, 'featureOverrides', 'VERIFIED', 'messageRetentionDays')}
                            fullWidth
                            margin="normal"
                            disabled={modalMode === 'view'}
                            InputProps={{ inputProps: { min: 1 } }}
                          />
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            )}

            {/* Moderation Overrides Tab */}
            {activeTab === 2 && (
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <Card variant="outlined">
                    <CardHeader title="BASIC Tier Moderation Overrides" />
                    <Divider />
                    <CardContent>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <FormControl fullWidth margin="normal">
                            <InputLabel>Strictness Level</InputLabel>
                            <Select
                              value={formData.moderationOverrides.BASIC.strictness}
                              onChange={(e) => handleInputChange(e, 'moderationOverrides', 'BASIC', 'strictness')}
                              label="Strictness Level"
                              disabled={modalMode === 'view'}
                            >
                              <MenuItem value="low">Low</MenuItem>
                              <MenuItem value="medium">Medium</MenuItem>
                              <MenuItem value="high">High</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>

                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.moderationOverrides.BASIC.autoReject}
                                onChange={(e) => handleInputChange(e, 'moderationOverrides', 'BASIC', 'autoReject')}
                                disabled={modalMode === 'view'}
                              />
                            }
                            label="Auto-Reject Inappropriate Messages"
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.moderationOverrides.BASIC.maskProfanity}
                                onChange={(e) => handleInputChange(e, 'moderationOverrides', 'BASIC', 'maskProfanity')}
                                disabled={modalMode === 'view'}
                              />
                            }
                            label="Mask Profanity"
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.moderationOverrides.BASIC.allowContactInfo}
                                onChange={(e) => handleInputChange(e, 'moderationOverrides', 'BASIC', 'allowContactInfo')}
                                disabled={modalMode === 'view'}
                              />
                            }
                            label="Allow Contact Information"
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FormControl
                            fullWidth
                            margin="normal"
                            disabled={modalMode === 'view' || !formData.moderationOverrides.BASIC.allowContactInfo}
                          >
                            <InputLabel>Allowed Contact Types</InputLabel>
                            <Select
                              multiple
                              value={formData.moderationOverrides.BASIC.allowedContactTypes}
                              onChange={(e) => handleInputChange(e, 'moderationOverrides', 'BASIC', 'allowedContactTypes')}
                              label="Allowed Contact Types"
                            >
                              <MenuItem value="email">Email Addresses</MenuItem>
                              <MenuItem value="phone">Phone Numbers</MenuItem>
                              <MenuItem value="url">Websites/URLs</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Card variant="outlined">
                    <CardHeader title="VERIFIED Tier Moderation Overrides" />
                    <Divider />
                    <CardContent>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <FormControl fullWidth margin="normal">
                            <InputLabel>Strictness Level</InputLabel>
                            <Select
                              value={formData.moderationOverrides.VERIFIED.strictness}
                              onChange={(e) => handleInputChange(e, 'moderationOverrides', 'VERIFIED', 'strictness')}
                              label="Strictness Level"
                              disabled={modalMode === 'view'}
                            >
                              <MenuItem value="low">Low</MenuItem>
                              <MenuItem value="medium">Medium</MenuItem>
                              <MenuItem value="high">High</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>

                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.moderationOverrides.VERIFIED.autoReject}
                                onChange={(e) => handleInputChange(e, 'moderationOverrides', 'VERIFIED', 'autoReject')}
                                disabled={modalMode === 'view'}
                              />
                            }
                            label="Auto-Reject Inappropriate Messages"
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.moderationOverrides.VERIFIED.maskProfanity}
                                onChange={(e) => handleInputChange(e, 'moderationOverrides', 'VERIFIED', 'maskProfanity')}
                                disabled={modalMode === 'view'}
                              />
                            }
                            label="Mask Profanity"
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FormControlLabel
                            control={
                              <Switch
                                checked={formData.moderationOverrides.VERIFIED.allowContactInfo}
                                onChange={(e) => handleInputChange(e, 'moderationOverrides', 'VERIFIED', 'allowContactInfo')}
                                disabled={modalMode === 'view'}
                              />
                            }
                            label="Allow Contact Information"
                          />
                        </Grid>

                        <Grid item xs={12}>
                          <FormControl
                            fullWidth
                            margin="normal"
                            disabled={modalMode === 'view' || !formData.moderationOverrides.VERIFIED.allowContactInfo}
                          >
                            <InputLabel>Allowed Contact Types</InputLabel>
                            <Select
                              multiple
                              value={formData.moderationOverrides.VERIFIED.allowedContactTypes}
                              onChange={(e) => handleInputChange(e, 'moderationOverrides', 'VERIFIED', 'allowedContactTypes')}
                              label="Allowed Contact Types"
                            >
                              <MenuItem value="email">Email Addresses</MenuItem>
                              <MenuItem value="phone">Phone Numbers</MenuItem>
                              <MenuItem value="url">Websites/URLs</MenuItem>
                            </Select>
                          </FormControl>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12}>
                  <Alert severity="info" sx={{ mt: 2 }}>
                    <AlertTitle>Moderation During Promotions</AlertTitle>
                    These settings will temporarily override the default moderation settings during the promotion period.
                    This can help prevent problems by relaxing certain restrictions while still maintaining appropriate content standards.
                  </Alert>
                </Grid>
              </Grid>
            )}

            {/* Display Settings Tab */}
            {activeTab === 3 && (
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    label="Promotion Title"
                    value={formData.display.title}
                    onChange={(e) => handleInputChange(e, 'display', null, 'title')}
                    fullWidth
                    margin="normal"
                    disabled={modalMode === 'view'}
                    required
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    label="Promotion Description"
                    value={formData.display.description}
                    onChange={(e) => handleInputChange(e, 'display', null, 'description')}
                    fullWidth
                    margin="normal"
                    multiline
                    rows={3}
                    disabled={modalMode === 'view'}
                    required
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Banner Color"
                    type="color"
                    value={formData.display.bannerColor}
                    onChange={(e) => handleInputChange(e, 'display', null, 'bannerColor')}
                    fullWidth
                    margin="normal"
                    disabled={modalMode === 'view'}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    label="Banner Text Color"
                    type="color"
                    value={formData.display.bannerTextColor}
                    onChange={(e) => handleInputChange(e, 'display', null, 'bannerTextColor')}
                    fullWidth
                    margin="normal"
                    disabled={modalMode === 'view'}
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.display.showCountdown}
                        onChange={(e) => handleInputChange(e, 'display', null, 'showCountdown')}
                        disabled={modalMode === 'view'}
                      />
                    }
                    label="Show Countdown Timer"
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.display.showOnHomepage}
                        onChange={(e) => handleInputChange(e, 'display', null, 'showOnHomepage')}
                        disabled={modalMode === 'view'}
                      />
                    }
                    label="Show on Homepage"
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.display.showInApp}
                        onChange={(e) => handleInputChange(e, 'display', null, 'showInApp')}
                        disabled={modalMode === 'view'}
                      />
                    }
                    label="Show In-App"
                  />
                </Grid>

                {/* Preview Banner */}
                <Grid item xs={12}>
                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 1,
                      bgcolor: formData.display.bannerColor,
                      color: formData.display.bannerTextColor,
                      mt: 2
                    }}
                  >
                    <Typography variant="h6">{formData.display.title || 'Promotion Title'}</Typography>
                    <Typography variant="body2">{formData.display.description || 'Promotion description goes here.'}</Typography>
                    {formData.display.showCountdown && (
                      <Typography variant="caption" sx={{ display: 'block', mt: 1 }}>
                        Ends in: 7 days (example)
                      </Typography>
                    )}
                  </Box>
                </Grid>
              </Grid>
            )}
          </DialogContent>

          <DialogActions>
            <Button onClick={() => setShowPromotionModal(false)}>
              {modalMode === 'view' ? 'Close' : 'Cancel'}
            </Button>

            {modalMode !== 'view' && (
              <Button
                onClick={handleSubmitForm}
                variant="contained"
                color="primary"
              >
                {modalMode === 'edit' ? 'Update' : 'Create'}
              </Button>
            )}
          </DialogActions>
        </Dialog>

        {/* Activate Promotion Modal */}
        <Dialog
          open={showActivateModal}
          onClose={() => setShowActivateModal(false)}
        >
          <DialogTitle>Activate Promotion</DialogTitle>

          <DialogContent>
            <Typography variant="body1" gutterBottom>
              Are you sure you want to activate the promotion "{selectedPromotion?.name}"?
            </Typography>

            <TextField
              label="Duration (Days)"
              type="number"
              value={activateDuration}
              onChange={(e) => setActivateDuration(parseInt(e.target.value))}
              fullWidth
              margin="normal"
              required
              InputProps={{ inputProps: { min: 1 } }}
            />
          </DialogContent>

          <DialogActions>
            <Button onClick={() => setShowActivateModal(false)}>Cancel</Button>
            <Button
              onClick={handleActivatePromotion}
              variant="contained"
              color="success"
            >
              Activate
            </Button>
          </DialogActions>
        </Dialog>

        {/* Deactivate Promotion Modal */}
        <Dialog
          open={showDeactivateModal}
          onClose={() => setShowDeactivateModal(false)}
        >
          <DialogTitle>Deactivate Promotion</DialogTitle>

          <DialogContent>
            <Typography variant="body1">
              Are you sure you want to deactivate the promotion "{selectedPromotion?.name}"?
            </Typography>
          </DialogContent>

          <DialogActions>
            <Button onClick={() => setShowDeactivateModal(false)}>Cancel</Button>
            <Button
              onClick={handleDeactivatePromotion}
              variant="contained"
              color="error"
            >
              Deactivate
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}

