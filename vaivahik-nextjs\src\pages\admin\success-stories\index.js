import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import dynamic from 'next/dynamic';

// Import EnhancedAdminLayout with SSR disabled to avoid localStorage issues
const EnhancedAdminLayout = dynamic(
  () => import('@/components/admin/EnhancedAdminLayout'),
  { ssr: false }
);
import { adminGet, adminDel } from '@/services/apiService';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  TextField,
  InputAdornment,
  Card,
  CardContent,
  Grid,
  Tooltip,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as VisibilityIcon,
  Search as SearchIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material';

export default function SuccessStories() {
  const router = useRouter();
  const [stories, setStories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [storyToDelete, setStoryToDelete] = useState(null);
  const [stats, setStats] = useState({
    totalStories: 0,
    publishedStories: 0,
    featuredStories: 0,
    averageMatchScore: 0,
    averageTimeToMatch: 0
  });

  // Fetch success stories
  const fetchStories = async () => {
    setLoading(true);
    try {
      const params = {
        page: page + 1,
        limit: rowsPerPage,
        search: searchQuery || undefined
      };
      
      const data = await adminGet('/success-stories', params);
      
      if (data.success) {
        setStories(data.stories || []);
        setTotalCount(data.pagination?.total || 0);
        setStats(data.stats || {});
      } else {
        setError('Failed to fetch success stories');
      }
    } catch (err) {
      console.error('Error fetching success stories:', err);
      setError('An error occurred while fetching success stories');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStories();
  }, [page, rowsPerPage, searchQuery]);

  // Handle page change
  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle search
  const handleSearch = (event) => {
    setSearchQuery(event.target.value);
    setPage(0);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (story) => {
    setStoryToDelete(story);
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const handleDeleteCancel = () => {
    setStoryToDelete(null);
    setDeleteDialogOpen(false);
  };

  // Delete success story
  const handleDeleteConfirm = async () => {
    if (!storyToDelete) return;
    
    try {
      const response = await adminDel(`/success-stories/${storyToDelete.id}`);
      
      if (response.success) {
        // Remove the deleted story from the list
        setStories(stories.filter(story => story.id !== storyToDelete.id));
        // Update stats
        setStats({
          ...stats,
          totalStories: stats.totalStories - 1,
          publishedStories: storyToDelete.status === 'published' ? stats.publishedStories - 1 : stats.publishedStories,
          featuredStories: storyToDelete.isFeatured ? stats.featuredStories - 1 : stats.featuredStories
        });
      } else {
        setError('Failed to delete success story');
      }
    } catch (err) {
      console.error('Error deleting success story:', err);
      setError('An error occurred while deleting the success story');
    } finally {
      setStoryToDelete(null);
      setDeleteDialogOpen(false);
    }
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <EnhancedAdminLayout title="Success Stories">
      <Box sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            Success Stories
          </Typography>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={() => router.push('/admin/success-stories/create')}
          >
            Add New Story
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Stories
                </Typography>
                <Typography variant="h4">
                  {stats.totalStories}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Published Stories
                </Typography>
                <Typography variant="h4">
                  {stats.publishedStories}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Average Match Score
                </Typography>
                <Typography variant="h4">
                  {stats.averageMatchScore}%
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Avg. Time to Match
                </Typography>
                <Typography variant="h4">
                  {stats.averageTimeToMatch} days
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Paper sx={{ mb: 3, p: 2 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <TextField
              label="Search Stories"
              variant="outlined"
              size="small"
              value={searchQuery}
              onChange={handleSearch}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
              sx={{ width: 300 }}
            />
            <Button
              variant="outlined"
              startIcon={<FilterListIcon />}
              onClick={() => {}}
            >
              Filter
            </Button>
          </Box>

          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : stories.length === 0 ? (
            <Alert severity="info">
              No success stories found. Create your first success story by clicking the "Add New Story" button.
            </Alert>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Title</TableCell>
                      <TableCell>Couple</TableCell>
                      <TableCell>Marriage Date</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Featured</TableCell>
                      <TableCell>Match Score</TableCell>
                      <TableCell>Published</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {stories.map((story) => (
                      <TableRow key={story.id}>
                        <TableCell>
                          <Link href={`/admin/success-stories/${story.id}`}>
                            {story.title}
                          </Link>
                        </TableCell>
                        <TableCell>{story.coupleNames}</TableCell>
                        <TableCell>{formatDate(story.marriageDate)}</TableCell>
                        <TableCell>
                          <Chip
                            label={story.status === 'published' ? 'Published' : 'Draft'}
                            color={story.status === 'published' ? 'success' : 'default'}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {story.isFeatured ? (
                            <StarIcon color="warning" />
                          ) : (
                            <StarBorderIcon color="action" />
                          )}
                        </TableCell>
                        <TableCell>{story.matchScore}%</TableCell>
                        <TableCell>{formatDate(story.publishedAt)}</TableCell>
                        <TableCell>
                          <Tooltip title="View">
                            <IconButton
                              size="small"
                              onClick={() => router.push(`/admin/success-stories/${story.id}`)}
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit">
                            <IconButton
                              size="small"
                              onClick={() => router.push(`/admin/success-stories/${story.id}/edit`)}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete">
                            <IconButton
                              size="small"
                              onClick={() => handleDeleteClick(story)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
              <TablePagination
                rowsPerPageOptions={[5, 10, 25]}
                component="div"
                count={totalCount}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
              />
            </>
          )}
        </Paper>
      </Box>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Success Story</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the success story "{storyToDelete?.title}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
    </EnhancedAdminLayout>
  );
}
