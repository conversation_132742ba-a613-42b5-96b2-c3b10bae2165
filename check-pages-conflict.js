const fs = require('fs');
const path = require('path');

// Function to recursively list all files in a directory
function listFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      listFiles(filePath, fileList);
    } else {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

// Check for conflicts between pages and src/pages
const pagesDir = path.join(__dirname, 'vaivahik-nextjs', 'pages');
const srcPagesDir = path.join(__dirname, 'vaivahik-nextjs', 'src', 'pages');

// Check if both directories exist
const pagesExists = fs.existsSync(pagesDir);
const srcPagesExists = fs.existsSync(srcPagesDir);

console.log(`pages directory exists: ${pagesExists}`);
console.log(`src/pages directory exists: ${srcPagesExists}`);

if (pagesExists && srcPagesExists) {
  // List all files in both directories
  const pagesFiles = listFiles(pagesDir);
  const srcPagesFiles = listFiles(srcPagesDir);
  
  // Convert to relative paths for easier comparison
  const pagesRelative = pagesFiles.map(file => path.relative(pagesDir, file));
  const srcPagesRelative = srcPagesFiles.map(file => path.relative(srcPagesDir, file));
  
  // Find conflicts (files with the same relative path)
  const conflicts = pagesRelative.filter(file => srcPagesRelative.includes(file));
  
  console.log(`Found ${conflicts.length} conflicts:`);
  conflicts.forEach(file => console.log(`- ${file}`));
} else {
  console.log('No conflicts possible as one of the directories does not exist.');
}

// Check for admin routes specifically
if (pagesExists) {
  const adminDir = path.join(pagesDir, 'admin');
  const adminExists = fs.existsSync(adminDir);
  console.log(`pages/admin directory exists: ${adminExists}`);
  
  if (adminExists) {
    const adminFiles = listFiles(adminDir);
    console.log(`Found ${adminFiles.length} files in pages/admin:`);
    adminFiles.forEach(file => console.log(`- ${path.relative(adminDir, file)}`));
  }
}

if (srcPagesExists) {
  const adminDir = path.join(srcPagesDir, 'admin');
  const adminExists = fs.existsSync(adminDir);
  console.log(`src/pages/admin directory exists: ${adminExists}`);
  
  if (adminExists) {
    const adminFiles = listFiles(adminDir);
    console.log(`Found ${adminFiles.length} files in src/pages/admin:`);
    adminFiles.forEach(file => console.log(`- ${path.relative(adminDir, file)}`));
  }
}
