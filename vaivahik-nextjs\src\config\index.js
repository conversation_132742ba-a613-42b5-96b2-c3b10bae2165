/**
 * Application Configuration
 * 
 * This file contains configuration settings for the application.
 */

// Default feature flags
export const FEATURE_FLAGS = {
  // Data source
  useRealBackend: false, // Whether to use real backend API or mock data

  // Feature toggles
  enableBiodataTemplates: true,
  enableSpotlightFeatures: true,
  enablePaymentGateway: false,
  enableNotifications: true,
  enableMatchingAlgorithm: true,
  
  // UI preferences
  enableDarkMode: false,
  enableAnimations: true,
  showMockDataIndicator: true,
  
  // Development
  enableDebugLogging: process.env.NODE_ENV === 'development'
};

// API configuration
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 30000, // 30 seconds
  retryAttempts: 3
};

// Backend API URL for compatibility with existing imports
export const BACKEND_API_URL = API_CONFIG.baseUrl;

// Authentication configuration
export const AUTH_CONFIG = {
  tokenKey: 'authToken',
  refreshTokenKey: 'refreshToken',
  tokenExpiry: 60 * 60 * 24 * 7, // 7 days in seconds
  loginPath: '/admin/login',
  dashboardPath: '/admin/dashboard'
};

// Privacy display name options
export const DISPLAY_NAME_OPTIONS = {
  FULL_NAME: {
    label: 'Full Name',
    description: 'Show complete name to everyone',
    icon: '👤',
    privacy: 'Low'
  },
  FIRST_NAME: {
    label: 'First Name Only',
    description: 'Show only first name for privacy',
    icon: '🔒',
    privacy: 'Medium'
  },
  PROFILE_ID: {
    label: 'Profile ID',
    description: 'Show only profile ID (e.g., M1234, F5678)',
    icon: '🆔',
    privacy: 'High'
  },
  ANONYMOUS: {
    label: 'Anonymous',
    description: 'Show as "Someone" for maximum privacy',
    icon: '👻',
    privacy: 'Maximum'
  }
};

// Default export for convenience
export default {
  FEATURE_FLAGS,
  API_CONFIG,
  AUTH_CONFIG,
  DISPLAY_NAME_OPTIONS
};
