/**
 * JavaScript wrapper for the Python ML Matching Service
 * This service calls the Python Flask API for ML-based matching
 */

const axios = require('axios');
const { PrismaClient } = require('@prisma/client');

class MLMatchingService {
  constructor() {
    this.pythonApiUrl = process.env.PYTHON_MATCHING_API_URL || 'http://localhost:5000';
    this.prisma = new PrismaClient();
    this.isServiceAvailable = false;
    this.checkServiceHealth();

    // Import phase manager and flexibility service
    this.PhaseManager = require('./phaseManager');
    this.FlexibilityService = require('./flexibilityService');
    this.phaseManager = new this.PhaseManager();
    this.flexibilityService = new this.FlexibilityService();
  }

  /**
   * Check if the Python ML service is available
   */
  async checkServiceHealth() {
    try {
      console.log(`Checking ML service health at: ${this.pythonApiUrl}/health`);
      const response = await axios.get(`${this.pythonApiUrl}/health`, { timeout: 5000 });
      this.isServiceAvailable = response.status === 200 && response.data.success;
      console.log('ML Matching Service:', this.isServiceAvailable ? 'Available' : 'Unavailable');
      if (this.isServiceAvailable) {
        console.log('ML Service Response:', response.data);
      }
    } catch (error) {
      this.isServiceAvailable = false;
      console.log('ML Matching Service: Unavailable (using fallback)');
      console.log('Error details:', error.code || error.message);
    }
  }

  /**
   * Get matches for a user using ML algorithm
   * @param {string} userId - User ID
   * @param {number} limit - Number of matches to return
   * @param {number} offset - Offset for pagination
   * @param {number} minScore - Minimum score threshold
   * @returns {Array} Array of matches with scores
   */
  async getMatches(userId, limit = 10, offset = 0, minScore = null) {
    try {
      // Get current user's profile and preferences
      const currentUser = await this.prisma.user.findUnique({
        where: { id: userId },
        include: { profile: true, preference: true }
      });

      if (!currentUser || !currentUser.profile) {
        return [];
      }

      // Get potential matches from database
      const targetGender = currentUser.profile.gender === 'Male' ? 'Female' : 'Male';
      const potentialMatches = await this.prisma.user.findMany({
        where: {
          id: { not: userId },
          isVerified: true,
          profile: {
            gender: targetGender
          }
        },
        include: {
          profile: true
        },
        take: Math.max(limit * 3, 50) // Get more candidates for ML to score
      });

      if (potentialMatches.length === 0) {
        return [];
      }

      // Get current phase and route to appropriate matching algorithm
      const currentPhase = await this.phaseManager.getCurrentPhase();
      console.log(`Using matching phase: ${currentPhase.version}`);

      // Route to appropriate matching algorithm based on phase
      switch (currentPhase.version) {
        case 'v1.5':
          return await this.getFlexibleMatches(currentUser, potentialMatches, limit, offset, minScore);
        case 'v2.0':
          return await this.getPersonalizedMatches(currentUser, potentialMatches, limit, offset, minScore);
        case 'v2.5':
          return await this.getIntelligentMatches(currentUser, potentialMatches, limit, offset, minScore);
        case 'v3.0':
          return await this.getAdvancedAIMatches(currentUser, potentialMatches, limit, offset, minScore);
        default:
          // v1.0 - Current rule-based matching
          return await this.getCurrentMatches(currentUser, potentialMatches, limit, offset, minScore);
      }

    } catch (error) {
      console.error('Error in getMatches:', error);
      return [];
    }
  }

  /**
   * Get matches using ML service
   */
  async getMLMatches(currentUser, potentialMatches, limit, offset, minScore) {
    const requestData = {
      user: {
        id: currentUser.id,
        profile: this.formatProfileForML(currentUser.profile)
      },
      preferences: currentUser.preference ? this.formatPreferencesForML(currentUser.preference) : {},
      potentialMatches: potentialMatches.map(match => ({
        id: match.id,
        profile: this.formatProfileForML(match.profile)
      }))
    };

    const response = await axios.post(`${this.pythonApiUrl}/api/match`, requestData, {
      timeout: 10000,
      headers: { 'Content-Type': 'application/json' }
    });

    if (!response.data.success) {
      throw new Error(response.data.message || 'ML service returned error');
    }

    let matches = response.data.matches;

    // Filter by minimum score if provided
    if (minScore) {
      matches = matches.filter(match => match.score >= minScore);
    }

    // Apply pagination
    matches = matches.slice(offset, offset + limit);

    // Enhance matches with profile data
    return await this.enhanceMatchesWithProfileData(matches, potentialMatches);
  }

  /**
   * Phase v1.0 - Current rule-based matching
   */
  async getCurrentMatches(currentUser, potentialMatches, limit, offset, minScore) {
    // If ML service is available, use it
    if (this.isServiceAvailable) {
      try {
        return await this.getMLMatches(currentUser, potentialMatches, limit, offset, minScore);
      } catch (error) {
        console.error('ML service error, falling back to rule-based:', error.message);
        this.isServiceAvailable = false;
      }
    }

    // Fallback to rule-based matching
    return await this.getRuleBasedMatches(currentUser, potentialMatches, limit, offset, minScore);
  }

  /**
   * Phase v1.5 - Flexible matching with compatibility groups
   */
  async getFlexibleMatches(currentUser, potentialMatches, limit, offset, minScore) {
    // Get user's flexibility settings
    const flexibilitySettings = await this.flexibilityService.getUserFlexibilitySettings(currentUser.id);

    const matches = potentialMatches.map(match => {
      let score = 50; // Base score

      // Flexible religion compatibility
      const religionScore = this.calculateReligionCompatibility(
        currentUser.profile.religion,
        match.profile.religion,
        flexibilitySettings.religionFlexible
      );
      score += religionScore * 0.3; // 30% weight

      // Flexible caste compatibility
      const casteScore = this.calculateCasteCompatibility(
        currentUser.profile.caste,
        match.profile.caste,
        flexibilitySettings.casteFlexible
      );
      score += casteScore * 0.25; // 25% weight

      // Flexible age compatibility
      const ageScore = this.calculateFlexibleAgeCompatibility(
        currentUser.profile.dateOfBirth,
        match.profile.dateOfBirth,
        flexibilitySettings.ageFlexibility || 5
      );
      score += ageScore * 0.2; // 20% weight

      // Education compatibility
      const educationScore = this.calculateEducationCompatibility(
        currentUser.profile.highestEducation,
        match.profile.highestEducation
      );
      score += educationScore * 0.15; // 15% weight

      // Location compatibility
      const locationScore = this.calculateLocationCompatibility(
        currentUser.profile.city,
        match.profile.city,
        currentUser.profile.state,
        match.profile.state
      );
      score += locationScore * 0.1; // 10% weight

      // Ensure score is between 0 and 100
      score = Math.min(100, Math.max(0, score));

      return {
        userId: match.id,
        score: Math.round(score),
        matchingPhase: 'v1.5',
        flexibilityApplied: flexibilitySettings.flexibilityLevel
      };
    });

    return this.processAndReturnMatches(matches, potentialMatches, limit, offset, minScore);
  }

  /**
   * Phase v2.0 - Personalized AI matching (placeholder)
   */
  async getPersonalizedMatches(currentUser, potentialMatches, limit, offset, minScore) {
    console.log('Personalized matching not yet implemented, falling back to flexible matching');
    return await this.getFlexibleMatches(currentUser, potentialMatches, limit, offset, minScore);
  }

  /**
   * Phase v2.5 - Intelligent features (placeholder)
   */
  async getIntelligentMatches(currentUser, potentialMatches, limit, offset, minScore) {
    console.log('Intelligent matching not yet implemented, falling back to flexible matching');
    return await this.getFlexibleMatches(currentUser, potentialMatches, limit, offset, minScore);
  }

  /**
   * Phase v3.0 - Advanced AI (placeholder)
   */
  async getAdvancedAIMatches(currentUser, potentialMatches, limit, offset, minScore) {
    console.log('Advanced AI matching not yet implemented, falling back to flexible matching');
    return await this.getFlexibleMatches(currentUser, potentialMatches, limit, offset, minScore);
  }

  /**
   * Original rule-based matching logic
   */
  async getRuleBasedMatches(currentUser, potentialMatches, limit, offset, minScore) {
    const matches = potentialMatches.map(match => {
      let score = 50; // Base score

      // Same religion bonus
      if (match.profile.religion === currentUser.profile.religion) {
        score += 20;
      }

      // Same caste bonus
      if (match.profile.caste === currentUser.profile.caste) {
        score += 15;
      }

      // Same sub-caste bonus
      if (match.profile.subCaste === currentUser.profile.subCaste) {
        score += 10;
      }

      // Education compatibility
      if (match.profile.highestEducation && currentUser.profile.highestEducation) {
        score += 8;
      }

      // Age compatibility (more flexible)
      if (match.profile.dateOfBirth && currentUser.profile.dateOfBirth) {
        const matchAge = new Date().getFullYear() - new Date(match.profile.dateOfBirth).getFullYear();
        const currentAge = new Date().getFullYear() - new Date(currentUser.profile.dateOfBirth).getFullYear();
        const ageDiff = Math.abs(matchAge - currentAge);

        // More flexible age scoring
        if (ageDiff <= 2) {
          score += 15; // Perfect age match
        } else if (ageDiff <= 4) {
          score += 12; // Very good match
        } else if (ageDiff <= 6) {
          score += 8;  // Good match
        } else if (ageDiff <= 10) {
          score += 4;  // Acceptable match
        } else if (ageDiff <= 15) {
          score += 1;  // Still possible match
        }
        // No penalty for larger age gaps - just no bonus
      }

      // Premium user bonus
      if (match.isPremium) {
        score += 5;
      }

      // Ensure score is between 0 and 100
      score = Math.min(100, Math.max(0, score));

      return {
        userId: match.id,
        score: score
      };
    });

    // Sort by score (descending)
    matches.sort((a, b) => b.score - a.score);

    // Filter by minimum score if provided
    let filteredMatches = matches;
    if (minScore) {
      filteredMatches = matches.filter(match => match.score >= minScore);
    }

    // Apply pagination
    const paginatedMatches = filteredMatches.slice(offset, offset + limit);

    // Enhance matches with profile data
    return await this.enhanceMatchesWithProfileData(paginatedMatches, potentialMatches);
  }

  /**
   * Enhance matches with full profile data
   */
  async enhanceMatchesWithProfileData(matches, potentialMatches) {
    return matches.map(match => {
      const fullProfile = potentialMatches.find(p => p.id === match.userId);
      if (!fullProfile) return match;

      return {
        ...match,
        profile: {
          fullName: fullProfile.profile.fullName,
          age: fullProfile.profile.dateOfBirth ?
            new Date().getFullYear() - new Date(fullProfile.profile.dateOfBirth).getFullYear() : null,
          height: fullProfile.profile.height,
          education: fullProfile.profile.highestEducation,
          occupation: fullProfile.profile.occupation,
          city: fullProfile.profile.city,
          religion: fullProfile.profile.religion,
          caste: fullProfile.profile.caste,
          subCaste: fullProfile.profile.subCaste
        },
        isPremium: fullProfile.isPremium,
        isVerified: fullProfile.isVerified
      };
    });
  }

  /**
   * Format profile data for ML service
   */
  formatProfileForML(profile) {
    return {
      gender: profile.gender,
      age: profile.dateOfBirth ?
        new Date().getFullYear() - new Date(profile.dateOfBirth).getFullYear() : null,
      height: profile.height ? parseFloat(profile.height) : null,
      religion: profile.religion,
      caste: profile.caste,
      subCaste: profile.subCaste,
      education: profile.highestEducation,
      occupation: profile.occupation,
      income: profile.annualIncome,
      maritalStatus: profile.maritalStatus,
      city: profile.city,
      state: profile.state
    };
  }

  /**
   * Format preferences for ML service
   */
  formatPreferencesForML(preferences) {
    return {
      ageMin: preferences.ageMin,
      ageMax: preferences.ageMax,
      heightMin: preferences.heightMin,
      heightMax: preferences.heightMax,
      education: preferences.education,
      occupation: preferences.occupation,
      income: preferences.income,
      location: preferences.location
    };
  }

  /**
   * Calculate religion compatibility with flexibility
   */
  calculateReligionCompatibility(userReligion, matchReligion, isFlexible) {
    if (userReligion === matchReligion) {
      return 50; // Perfect match
    }

    if (!isFlexible) {
      return 5; // Low score for different religions when not flexible
    }

    // Religion compatibility groups
    const religionGroups = {
      'Hindu': ['Hindu', 'Jain', 'Buddhist', 'Sikh'],
      'Muslim': ['Muslim', 'Sufi'],
      'Christian': ['Christian', 'Catholic', 'Protestant']
    };

    for (const group of Object.values(religionGroups)) {
      if (group.includes(userReligion) && group.includes(matchReligion)) {
        return 35; // Good compatibility within group
      }
    }

    return 15; // Some compatibility when flexible
  }

  /**
   * Calculate caste compatibility with flexibility
   */
  calculateCasteCompatibility(userCaste, matchCaste, isFlexible) {
    if (userCaste === matchCaste) {
      return 40; // Perfect match
    }

    if (!isFlexible) {
      return 5; // Low score for different castes when not flexible
    }

    // Caste compatibility groups (Maratha community focused)
    const casteGroups = {
      'Maratha': ['Maratha', 'Kunbi', 'Mali', 'Dhangar'],
      'Brahmin': ['Brahmin', 'Deshastha', 'Chitpavan', 'Karhade']
    };

    for (const group of Object.values(casteGroups)) {
      if (group.includes(userCaste) && group.includes(matchCaste)) {
        return 25; // Good compatibility within group
      }
    }

    return 10; // Some compatibility when flexible
  }

  /**
   * Calculate flexible age compatibility
   */
  calculateFlexibleAgeCompatibility(userDOB, matchDOB, ageFlexibility) {
    if (!userDOB || !matchDOB) {
      return 10; // Default score when age unknown
    }

    const userAge = new Date().getFullYear() - new Date(userDOB).getFullYear();
    const matchAge = new Date().getFullYear() - new Date(matchDOB).getFullYear();
    const ageDiff = Math.abs(userAge - matchAge);

    if (ageDiff === 0) {
      return 30; // Perfect age match
    } else if (ageDiff <= 2) {
      return 25; // Excellent match
    } else if (ageDiff <= ageFlexibility) {
      return 20 - (ageDiff * 2); // Good match within flexibility range
    } else if (ageDiff <= ageFlexibility + 5) {
      return 10 - ageDiff; // Acceptable match slightly outside range
    } else {
      return 2; // Poor match outside flexibility range
    }
  }

  /**
   * Calculate education compatibility
   */
  calculateEducationCompatibility(userEducation, matchEducation) {
    if (!userEducation || !matchEducation) {
      return 8; // Default score when education unknown
    }

    if (userEducation === matchEducation) {
      return 20; // Perfect match
    }

    // Education level hierarchy
    const educationLevels = {
      'PhD': 7,
      'Masters': 6,
      'Post Graduate': 5,
      'Graduate': 4,
      'Diploma': 3,
      'Higher Secondary': 2,
      'Secondary': 1
    };

    const userLevel = educationLevels[userEducation] || 0;
    const matchLevel = educationLevels[matchEducation] || 0;
    const levelDiff = Math.abs(userLevel - matchLevel);

    if (levelDiff <= 1) {
      return 15; // Very compatible
    } else if (levelDiff <= 2) {
      return 10; // Compatible
    } else {
      return 5; // Less compatible
    }
  }

  /**
   * Calculate location compatibility
   */
  calculateLocationCompatibility(userCity, matchCity, userState, matchState) {
    if (userCity === matchCity) {
      return 15; // Same city
    } else if (userState === matchState) {
      return 10; // Same state
    } else {
      return 5; // Different state
    }
  }

  /**
   * Process and return matches with common logic
   */
  processAndReturnMatches(matches, potentialMatches, limit, offset, minScore) {
    // Sort by score (descending)
    matches.sort((a, b) => b.score - a.score);

    // Filter by minimum score if provided
    let filteredMatches = matches;
    if (minScore) {
      filteredMatches = matches.filter(match => match.score >= minScore);
    }

    // Apply pagination
    const paginatedMatches = filteredMatches.slice(offset, offset + limit);

    // Enhance matches with profile data
    return this.enhanceMatchesWithProfileData(paginatedMatches, potentialMatches);
  }

  /**
   * Get match explanation
   */
  async getMatchExplanation(userId, matchId) {
    try {
      if (!this.isServiceAvailable) {
        // Return simplified explanation
        return {
          matchId,
          overallScore: 75,
          factors: [
            { name: 'Religion', score: 100, weight: 30 },
            { name: 'Caste', score: 100, weight: 25 },
            { name: 'Education', score: 80, weight: 20 },
            { name: 'Age', score: 70, weight: 15 },
            { name: 'Location', score: 60, weight: 10 }
          ]
        };
      }

      // Get user and match profiles
      const [user, match] = await Promise.all([
        this.prisma.user.findUnique({
          where: { id: userId },
          include: { profile: true, preference: true }
        }),
        this.prisma.user.findUnique({
          where: { id: matchId },
          include: { profile: true }
        })
      ]);

      if (!user || !match) {
        throw new Error('User or match not found');
      }

      const requestData = {
        user: {
          id: user.id,
          profile: this.formatProfileForML(user.profile)
        },
        preferences: user.preference ? this.formatPreferencesForML(user.preference) : {},
        match: {
          id: match.id,
          profile: this.formatProfileForML(match.profile)
        }
      };

      const response = await axios.post(`${this.pythonApiUrl}/api/match-analysis`, requestData, {
        timeout: 5000,
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.data.success) {
        throw new Error(response.data.message || 'ML service returned error');
      }

      return response.data;

    } catch (error) {
      console.error('Error getting match explanation:', error);
      // Return fallback explanation
      return {
        matchId,
        overallScore: 75,
        factors: [
          { name: 'Religion', score: 100, weight: 30 },
          { name: 'Caste', score: 100, weight: 25 },
          { name: 'Education', score: 80, weight: 20 },
          { name: 'Age', score: 70, weight: 15 },
          { name: 'Location', score: 60, weight: 10 }
        ]
      };
    }
  }
}

module.exports = new MLMatchingService();
