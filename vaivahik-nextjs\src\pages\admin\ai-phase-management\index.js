import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Stack,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  LinearProgress,
  Stepper,
  Step,
  StepLabel,
  StepContent
} from '@mui/material';
import {
  Psychology as AIIcon,
  TrendingUp as PhaseIcon,
  Settings as SettingsIcon,
  Assessment as AnalyticsIcon,
  Build as FeaturesIcon,
  Timeline as ProgressIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Storage as StorageIcon,
  NetworkCheck as NetworkIcon,
  Upgrade as UpgradeIcon,
  PlayArrow as ExecuteIcon
} from '@mui/icons-material';
import { toast } from 'react-toastify';
import { adminGet, adminPost, adminPut } from '@/utils/api';

// Dynamic import for EnhancedAdminLayout to avoid SSR issues
const EnhancedAdminLayout = dynamic(() => import('@/components/admin/EnhancedAdminLayout'), {
  ssr: false
});

export default function AIPhaseManagement() {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [currentPhase, setCurrentPhase] = useState({});
  const [allPhases, setAllPhases] = useState([]);
  const [phaseMetrics, setPhaseMetrics] = useState({});
  const [dataQuality, setDataQuality] = useState({});
  const [upgradeDialogOpen, setUpgradeDialogOpen] = useState(false);
  const [selectedPhase, setSelectedPhase] = useState('');
  const [featureTestDialog, setFeatureTestDialog] = useState(false);
  const [selectedFeature, setSelectedFeature] = useState('');

  // Mock data for demonstration
  useEffect(() => {
    fetchPhaseData();
  }, []);

  const fetchPhaseData = async () => {
    try {
      setLoading(true);
      
      // Mock current phase data
      setCurrentPhase({
        currentPhase: 'LEARNING',
        phaseName: 'Learning Phase',
        description: 'Collect user behavior data and implement basic ML',
        userRange: '100 - 1000',
        availableFeatures: [
          { name: 'basic_matching', implementation: 'rule_based', complexity: 'low' },
          { name: 'profile_scoring', implementation: 'weighted_scoring', complexity: 'low' },
          { name: 'preference_learning', implementation: 'collaborative_filtering', complexity: 'medium' },
          { name: 'simple_recommendations', implementation: 'content_based', complexity: 'medium' }
        ],
        nextPhase: 'INTELLIGENT',
        previousPhase: 'BOOTSTRAP'
      });

      // Mock all phases data
      setAllPhases([
        {
          phaseName: 'BOOTSTRAP',
          name: 'Bootstrap Phase',
          minUsers: 0,
          maxUsers: 100,
          features: ['basic_matching', 'profile_scoring'],
          description: 'Basic rule-based matching for initial users',
          isCurrent: false
        },
        {
          phaseName: 'LEARNING',
          name: 'Learning Phase',
          minUsers: 100,
          maxUsers: 1000,
          features: ['basic_matching', 'profile_scoring', 'preference_learning', 'simple_recommendations'],
          description: 'Collect user behavior data and implement basic ML',
          isCurrent: true
        },
        {
          phaseName: 'INTELLIGENT',
          name: 'Intelligent Phase',
          minUsers: 1000,
          maxUsers: 10000,
          features: ['advanced_matching', 'compatibility_prediction', 'smart_recommendations', 'fraud_detection'],
          description: 'Advanced ML algorithms with sufficient training data',
          isCurrent: false
        },
        {
          phaseName: 'ADVANCED',
          name: 'Advanced Phase',
          minUsers: 10000,
          maxUsers: 100000,
          features: ['deep_learning_matching', 'behavioral_analysis', 'success_prediction', 'personalization'],
          description: 'Deep learning models with comprehensive features',
          isCurrent: false
        },
        {
          phaseName: 'ENTERPRISE',
          name: 'Enterprise Phase',
          minUsers: 100000,
          maxUsers: Infinity,
          features: ['neural_matching', 'real_time_optimization', 'advanced_analytics', 'predictive_modeling'],
          description: 'Enterprise-grade AI with real-time optimization',
          isCurrent: false
        }
      ]);

      // Mock phase metrics
      setPhaseMetrics({
        currentUsers: 450,
        dataQualityScore: 0.75,
        algorithmPerformance: 0.82,
        featureUtilization: 0.68,
        systemLoad: 0.45,
        recommendedPhase: 'LEARNING',
        canUpgrade: false,
        upgradeRequirements: [
          'Need 550 more users (minimum 1000)',
          'Improve data quality score to 0.8+',
          'Complete preference learning model training'
        ]
      });

      // Mock data quality metrics
      setDataQuality({
        profileCompleteness: 0.78,
        interactionData: 0.65,
        feedbackData: 0.72,
        successStories: 0.45,
        dataConsistency: 0.92,
        overall: 0.75
      });

    } catch (error) {
      console.error('Error fetching phase data:', error);
      toast.error('Error fetching AI phase data');
    } finally {
      setLoading(false);
    }
  };

  const handlePhaseUpgrade = async () => {
    try {
      // Mock phase upgrade
      toast.success(`Phase upgraded to ${selectedPhase}`);
      setUpgradeDialogOpen(false);
      await fetchPhaseData();
    } catch (error) {
      toast.error('Error upgrading phase');
    }
  };

  const handleFeatureTest = async () => {
    try {
      // Mock feature test
      toast.success(`Feature ${selectedFeature} tested successfully`);
      setFeatureTestDialog(false);
    } catch (error) {
      toast.error('Error testing feature');
    }
  };

  const getPhaseChip = (phase) => {
    const colors = {
      BOOTSTRAP: 'default',
      LEARNING: 'primary',
      INTELLIGENT: 'secondary',
      ADVANCED: 'success',
      ENTERPRISE: 'warning'
    };
    
    return (
      <Chip 
        label={phase.name} 
        color={colors[phase.phaseName] || 'default'} 
        variant={phase.isCurrent ? 'filled' : 'outlined'}
        icon={phase.isCurrent ? <CheckCircleIcon /> : undefined}
      />
    );
  };

  const getComplexityChip = (complexity) => {
    const colors = {
      low: 'success',
      medium: 'warning',
      high: 'error',
      very_high: 'error',
      extreme: 'error'
    };
    
    return <Chip label={complexity} color={colors[complexity]} size="small" />;
  };

  const TabPanel = ({ children, value, index, ...other }) => (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`ai-tabpanel-${index}`}
      aria-labelledby={`ai-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );

  return (
    <EnhancedAdminLayout title="AI Phase Management">
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            AI Algorithm Phase Management
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<UpgradeIcon />}
              onClick={() => setUpgradeDialogOpen(true)}
              disabled={!phaseMetrics.canUpgrade}
            >
              Upgrade Phase
            </Button>
            <Button
              variant="contained"
              startIcon={<ExecuteIcon />}
              onClick={() => setFeatureTestDialog(true)}
            >
              Test Feature
            </Button>
          </Box>
        </Box>

        {/* Current Phase Overview */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Current Phase
                    </Typography>
                    {getPhaseChip({ name: currentPhase.phaseName, phaseName: currentPhase.currentPhase, isCurrent: true })}
                  </Box>
                  <AIIcon color="primary" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Current Users
                    </Typography>
                    <Typography variant="h4" component="div">
                      {phaseMetrics.currentUsers?.toLocaleString() || 0}
                    </Typography>
                  </Box>
                  <PhaseIcon color="success" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Data Quality
                    </Typography>
                    <Typography variant="h4" component="div">
                      {(phaseMetrics.dataQualityScore * 100).toFixed(0)}%
                    </Typography>
                  </Box>
                  <StorageIcon color="info" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} md={3}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box>
                    <Typography color="text.secondary" gutterBottom variant="body2">
                      Performance
                    </Typography>
                    <Typography variant="h4" component="div">
                      {(phaseMetrics.algorithmPerformance * 100).toFixed(0)}%
                    </Typography>
                  </Box>
                  <SpeedIcon color="warning" sx={{ fontSize: 40 }} />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Data Quality Metrics */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Data Quality Breakdown
            </Typography>
            <Grid container spacing={3}>
              {Object.entries(dataQuality).map(([metric, value]) => (
                <Grid item xs={12} md={2} key={metric}>
                  <Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {metric.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={value * 100}
                      sx={{ height: 8, borderRadius: 4, mb: 1 }}
                      color={value > 0.8 ? 'success' : value > 0.6 ? 'warning' : 'error'}
                    />
                    <Typography variant="body2" fontWeight="600">
                      {(value * 100).toFixed(1)}%
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>

        {/* Upgrade Requirements */}
        {!phaseMetrics.canUpgrade && (
          <Alert severity="info" sx={{ mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              Requirements for Next Phase Upgrade:
            </Typography>
            <List dense>
              {phaseMetrics.upgradeRequirements?.map((requirement, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <InfoIcon color="info" />
                  </ListItemIcon>
                  <ListItemText primary={requirement} />
                </ListItem>
              ))}
            </List>
          </Alert>
        )}

        {/* Tabs */}
        <Paper sx={{ mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, newValue) => setActiveTab(newValue)}>
            <Tab label="Phase Overview" icon={<PhaseIcon />} />
            <Tab label="Available Features" icon={<FeaturesIcon />} />
            <Tab label="All Phases" icon={<ProgressIcon />} />
            <Tab label="Analytics" icon={<AnalyticsIcon />} />
          </Tabs>
        </Paper>

        {/* Tab Panels */}
        <TabPanel value={activeTab} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    {currentPhase.phaseName} - {currentPhase.description}
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    User Range: {currentPhase.userRange}
                  </Typography>
                  
                  <Box sx={{ mt: 3 }}>
                    <Typography variant="h6" gutterBottom>
                      Phase Progression
                    </Typography>
                    <Stepper orientation="vertical">
                      {allPhases.map((phase, index) => (
                        <Step key={phase.phaseName} active={phase.isCurrent} completed={index < allPhases.findIndex(p => p.isCurrent)}>
                          <StepLabel>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body1" fontWeight={phase.isCurrent ? 600 : 400}>
                                {phase.name}
                              </Typography>
                              {phase.isCurrent && <Chip label="Current" color="primary" size="small" />}
                            </Box>
                          </StepLabel>
                          <StepContent>
                            <Typography variant="body2" color="text.secondary">
                              {phase.description}
                            </Typography>
                            <Typography variant="caption" display="block">
                              Users: {phase.minUsers} - {phase.maxUsers === Infinity ? '∞' : phase.maxUsers}
                            </Typography>
                          </StepContent>
                        </Step>
                      ))}
                    </Stepper>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={4}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    System Metrics
                  </Typography>
                  <Stack spacing={2}>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        Feature Utilization
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={phaseMetrics.featureUtilization * 100}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                      <Typography variant="body2">
                        {(phaseMetrics.featureUtilization * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        System Load
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={phaseMetrics.systemLoad * 100}
                        sx={{ height: 8, borderRadius: 4 }}
                        color={phaseMetrics.systemLoad > 0.8 ? 'error' : 'primary'}
                      />
                      <Typography variant="body2">
                        {(phaseMetrics.systemLoad * 100).toFixed(1)}%
                      </Typography>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={activeTab} index={1}>
          <Typography variant="h6" gutterBottom>
            Available Features in Current Phase
          </Typography>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Feature Name</TableCell>
                  <TableCell>Implementation</TableCell>
                  <TableCell>Complexity</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {currentPhase.availableFeatures?.map((feature, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <Typography variant="body1" fontWeight="600">
                        {feature.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Typography>
                    </TableCell>
                    <TableCell>{feature.implementation}</TableCell>
                    <TableCell>{getComplexityChip(feature.complexity)}</TableCell>
                    <TableCell>
                      <Chip label="Active" color="success" size="small" />
                    </TableCell>
                    <TableCell>
                      <Button
                        size="small"
                        onClick={() => {
                          setSelectedFeature(feature.name);
                          setFeatureTestDialog(true);
                        }}
                      >
                        Test
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={activeTab} index={2}>
          <Typography variant="h6" gutterBottom>
            All AI Algorithm Phases
          </Typography>
          <Grid container spacing={3}>
            {allPhases.map((phase, index) => (
              <Grid item xs={12} md={6} lg={4} key={phase.phaseName}>
                <Card sx={{ height: '100%', border: phase.isCurrent ? 2 : 0, borderColor: 'primary.main' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6">
                        {phase.name}
                      </Typography>
                      {getPhaseChip(phase)}
                    </Box>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {phase.description}
                    </Typography>
                    <Typography variant="caption" display="block" gutterBottom>
                      Users: {phase.minUsers} - {phase.maxUsers === Infinity ? '∞' : phase.maxUsers}
                    </Typography>
                    <Typography variant="body2" fontWeight="600" gutterBottom>
                      Features ({phase.features.length}):
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {phase.features.map((feature, i) => (
                        <Chip key={i} label={feature} size="small" variant="outlined" />
                      ))}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </TabPanel>

        <TabPanel value={activeTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Algorithm Performance
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Performance metrics and analytics would be displayed here with charts and detailed breakdowns.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Feature Usage Statistics
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Feature usage statistics and optimization recommendations would be shown here.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Phase Upgrade Dialog */}
        <Dialog open={upgradeDialogOpen} onClose={() => setUpgradeDialogOpen(false)}>
          <DialogTitle>Upgrade AI Phase</DialogTitle>
          <DialogContent>
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>Select Target Phase</InputLabel>
              <Select
                value={selectedPhase}
                label="Select Target Phase"
                onChange={(e) => setSelectedPhase(e.target.value)}
              >
                {allPhases.filter(phase => !phase.isCurrent).map((phase) => (
                  <MenuItem key={phase.phaseName} value={phase.phaseName}>
                    {phase.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setUpgradeDialogOpen(false)}>Cancel</Button>
            <Button onClick={handlePhaseUpgrade} variant="contained">
              Upgrade Phase
            </Button>
          </DialogActions>
        </Dialog>

        {/* Feature Test Dialog */}
        <Dialog open={featureTestDialog} onClose={() => setFeatureTestDialog(false)}>
          <DialogTitle>Test AI Feature</DialogTitle>
          <DialogContent>
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>Select Feature</InputLabel>
              <Select
                value={selectedFeature}
                label="Select Feature"
                onChange={(e) => setSelectedFeature(e.target.value)}
              >
                {currentPhase.availableFeatures?.map((feature) => (
                  <MenuItem key={feature.name} value={feature.name}>
                    {feature.name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setFeatureTestDialog(false)}>Cancel</Button>
            <Button onClick={handleFeatureTest} variant="contained">
              Test Feature
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </EnhancedAdminLayout>
  );
}
