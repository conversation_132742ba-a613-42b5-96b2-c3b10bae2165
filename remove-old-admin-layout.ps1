# PowerShell script to safely remove the old AdminLayout.js file
# This script checks if all admin pages are using EnhancedAdminLayout before removing AdminLayout.js

# Function to check if any admin pages are still using AdminLayout
function Check-AdminLayoutUsage {
    $adminPagesDir = "vaivahik-nextjs\src\pages\admin"
    $usingAdminLayout = @()
    
    # Get all JS files in the admin pages directory
    $adminPages = Get-ChildItem -Path $adminPagesDir -Filter "*.js" -Recurse
    
    foreach ($page in $adminPages) {
        $content = Get-Content -Path $page.FullName -Raw
        
        # Check if the page imports AdminLayout
        if ($content -match "import AdminLayout from") {
            $usingAdminLayout += $page.FullName
        }
    }
    
    return $usingAdminLayout
}

# Check if any admin pages are still using AdminLayout
$pagesUsingAdminLayout = Check-AdminLayoutUsage

if ($pagesUsingAdminLayout.Count -gt 0) {
    Write-Host "ERROR: The following admin pages are still using AdminLayout:" -ForegroundColor Red
    foreach ($page in $pagesUsingAdminLayout) {
        Write-Host "  - $page" -ForegroundColor Red
    }
    Write-Host "Please update these pages to use EnhancedAdminLayout before removing AdminLayout.js." -ForegroundColor Red
    exit 1
}

# If no pages are using AdminLayout, we can safely remove it
$adminLayoutPath = "vaivahik-nextjs\src\components\admin\AdminLayout.js"

if (Test-Path $adminLayoutPath) {
    # Check if backup exists
    $backupPath = "vaivahik-nextjs\src\components\admin\AdminLayout.backup.js"
    
    if (-not (Test-Path $backupPath)) {
        Write-Host "WARNING: No backup of AdminLayout.js found at $backupPath" -ForegroundColor Yellow
        $confirmation = Read-Host "Do you want to create a backup before removing? (Y/N)"
        
        if ($confirmation -eq "Y" -or $confirmation -eq "y") {
            Copy-Item -Path $adminLayoutPath -Destination $backupPath
            Write-Host "Backup created at $backupPath" -ForegroundColor Green
        }
    }
    
    # Remove the file
    Remove-Item -Path $adminLayoutPath
    Write-Host "Successfully removed $adminLayoutPath" -ForegroundColor Green
    
    # Check if it was removed
    if (-not (Test-Path $adminLayoutPath)) {
        Write-Host "Verified: AdminLayout.js has been removed." -ForegroundColor Green
    } else {
        Write-Host "WARNING: Failed to remove AdminLayout.js" -ForegroundColor Red
    }
} else {
    Write-Host "AdminLayout.js not found at $adminLayoutPath" -ForegroundColor Yellow
}

# Check for any references to AdminLayout in the codebase
Write-Host "Checking for any remaining references to AdminLayout..." -ForegroundColor Cyan

$referencesFound = $false
$rootDir = "vaivahik-nextjs"
$excludeBackup = "AdminLayout.backup.js"
$excludeScripts = "remove-old-admin-layout.ps1", "update-admin-layout.ps1", "find-admin-layout-usage.js", "update-remaining-layouts.js"

Get-ChildItem -Path $rootDir -Recurse -File -Include "*.js", "*.jsx", "*.ts", "*.tsx" | 
    Where-Object { $_.Name -ne $excludeBackup -and $_.Name -notin $excludeScripts } | 
    ForEach-Object {
        $content = Get-Content -Path $_.FullName -Raw
        if ($content -match "AdminLayout" -and $content -notmatch "EnhancedAdminLayout") {
            Write-Host "  - Reference found in: $($_.FullName)" -ForegroundColor Yellow
            $referencesFound = $true
        }
    }

if (-not $referencesFound) {
    Write-Host "No remaining references to AdminLayout found." -ForegroundColor Green
} else {
    Write-Host "WARNING: Some references to AdminLayout still exist in the codebase." -ForegroundColor Yellow
    Write-Host "These might be harmless (e.g., comments or strings), but you should check them to be sure." -ForegroundColor Yellow
}

Write-Host "Done!" -ForegroundColor Green
