import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Avatar,
  Button,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Paper,
  Tooltip,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Visibility as ViewIcon,
  Message as MessageIcon,
  Person as PersonIcon,
  Favorite as FavoriteIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Share as ShareIcon,
  Note as NoteIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

export default function ShortlistPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [shortlist, setShortlist] = useState([]);
  const [selectedProfile, setSelectedProfile] = useState(null);
  const [noteDialog, setNoteDialog] = useState(false);
  const [note, setNote] = useState('');
  const [actionLoading, setActionLoading] = useState(false);
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedProfileId, setSelectedProfileId] = useState(null);

  useEffect(() => {
    fetchShortlist();
  }, []);

  const fetchShortlist = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/shortlist', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setShortlist(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching shortlist:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveFromShortlist = async (profileId) => {
    setActionLoading(true);
    try {
      const response = await fetch(`/api/user/shortlist/${profileId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        setShortlist(shortlist.filter(item => item.profile.id !== profileId));
      }
    } catch (error) {
      console.error('Error removing from shortlist:', error);
    } finally {
      setActionLoading(false);
      setAnchorEl(null);
    }
  };

  const handleAddNote = async (profileId, noteText) => {
    setActionLoading(true);
    try {
      const response = await fetch(`/api/user/shortlist/${profileId}/note`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ note: noteText })
      });

      if (response.ok) {
        await fetchShortlist(); // Refresh the list
        setNoteDialog(false);
        setNote('');
        setSelectedProfile(null);
      }
    } catch (error) {
      console.error('Error adding note:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleSendInterest = async (profileId) => {
    setActionLoading(true);
    try {
      const response = await fetch(`/api/user/interests/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ 
          targetUserId: profileId,
          message: 'I found your profile interesting and would like to connect.'
        })
      });

      if (response.ok) {
        // Update the shortlist item to reflect interest sent
        setShortlist(shortlist.map(item => 
          item.profile.id === profileId 
            ? { ...item, interestSent: true }
            : item
        ));
      }
    } catch (error) {
      console.error('Error sending interest:', error);
    } finally {
      setActionLoading(false);
      setAnchorEl(null);
    }
  };

  const handleViewProfile = (userId) => {
    router.push(`/profile/${userId}`);
  };

  const handleMenuOpen = (event, profileId) => {
    setAnchorEl(event.currentTarget);
    setSelectedProfileId(profileId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedProfileId(null);
  };

  const ShortlistCard = ({ item }) => (
    <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column', '&:hover': { boxShadow: 6 } }}>
      <CardContent sx={{ flexGrow: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            src={item.profile?.profilePicture}
            sx={{ width: 60, height: 60, mr: 2 }}
          >
            <PersonIcon />
          </Avatar>
          <Box sx={{ flexGrow: 1 }}>
            <Typography variant="h6" gutterBottom>
              {item.profile?.firstName} {item.profile?.lastName}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {item.profile?.age} years • {item.profile?.location}
            </Typography>
          </Box>
          <IconButton
            onClick={(e) => handleMenuOpen(e, item.profile?.id)}
            size="small"
          >
            <MoreVertIcon />
          </IconButton>
        </Box>

        <Typography variant="body2" color="text.secondary" gutterBottom>
          <strong>Education:</strong> {item.profile?.education}
        </Typography>
        <Typography variant="body2" color="text.secondary" gutterBottom>
          <strong>Occupation:</strong> {item.profile?.occupation}
        </Typography>

        {item.profile?.compatibility && (
          <Chip
            label={`${item.profile.compatibility}% Match`}
            color="primary"
            size="small"
            sx={{ mt: 1, mb: 1 }}
          />
        )}

        {item.interestSent && (
          <Chip
            label="Interest Sent"
            color="success"
            size="small"
            sx={{ mt: 1, mb: 1, ml: 1 }}
          />
        )}

        {item.note && (
          <Box sx={{ mt: 2, p: 1, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="caption" color="text.secondary">
              <NoteIcon sx={{ fontSize: 12, mr: 0.5 }} />
              Your Note:
            </Typography>
            <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
              {item.note}
            </Typography>
          </Box>
        )}

        <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
          Added on {format(new Date(item.createdAt), 'MMM dd, yyyy')}
        </Typography>
      </CardContent>

      <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
        <Button
          size="small"
          variant="outlined"
          startIcon={<ViewIcon />}
          onClick={() => handleViewProfile(item.profile?.id)}
        >
          View Profile
        </Button>
        
        {!item.interestSent ? (
          <Button
            size="small"
            variant="contained"
            startIcon={<FavoriteIcon />}
            onClick={() => handleSendInterest(item.profile?.id)}
            disabled={actionLoading}
          >
            Send Interest
          </Button>
        ) : (
          <Button
            size="small"
            variant="contained"
            startIcon={<MessageIcon />}
            onClick={() => router.push(`/messages?user=${item.profile?.id}`)}
          >
            Message
          </Button>
        )}
      </CardActions>
    </Card>
  );

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading your shortlist...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <BookmarkIcon sx={{ mr: 2, fontSize: 32, color: 'primary.main' }} />
        <Typography variant="h4">
          My Shortlist ({shortlist.length})
        </Typography>
      </Box>

      {shortlist.length > 0 ? (
        <Grid container spacing={3}>
          {shortlist.map((item) => (
            <Grid item xs={12} sm={6} md={4} key={item.id}>
              <ShortlistCard item={item} />
            </Grid>
          ))}
        </Grid>
      ) : (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <BookmarkBorderIcon sx={{ fontSize: 64, color: 'grey.400', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            Your shortlist is empty
          </Typography>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Start browsing profiles and add interesting ones to your shortlist for easy access later.
          </Typography>
          <Button
            variant="contained"
            sx={{ mt: 2 }}
            onClick={() => router.push('/search')}
          >
            Browse Profiles
          </Button>
        </Paper>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem
          onClick={() => {
            setSelectedProfile(shortlist.find(item => item.profile?.id === selectedProfileId));
            setNote(shortlist.find(item => item.profile?.id === selectedProfileId)?.note || '');
            setNoteDialog(true);
            handleMenuClose();
          }}
        >
          <NoteIcon sx={{ mr: 1 }} />
          {shortlist.find(item => item.profile?.id === selectedProfileId)?.note ? 'Edit Note' : 'Add Note'}
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleRemoveFromShortlist(selectedProfileId);
          }}
          sx={{ color: 'error.main' }}
        >
          <DeleteIcon sx={{ mr: 1 }} />
          Remove from Shortlist
        </MenuItem>
      </Menu>

      {/* Note Dialog */}
      <Dialog
        open={noteDialog}
        onClose={() => setNoteDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Add Note for {selectedProfile?.profile?.firstName}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Add a personal note to remember why you shortlisted this profile:
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            placeholder="e.g., Great personality, similar interests, family background matches..."
            value={note}
            onChange={(e) => setNote(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNoteDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => handleAddNote(selectedProfile?.profile?.id, note)}
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={20} /> : 'Save Note'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
