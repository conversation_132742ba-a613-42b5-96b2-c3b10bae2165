-- Add User Behavior Tracking table
CREATE TABLE "user_behaviors" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "behavior_type" TEXT NOT NULL,
    "target_user_id" TEXT,
    "metadata" TEXT,
    "weight" DOUBLE PRECISION NOT NULL DEFAULT 0,
    "category" TEXT NOT NULL,
    "timestamp" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_behaviors_pkey" PRIMARY KEY ("id")
);

-- Add indexes for performance
CREATE INDEX "user_behaviors_user_id_idx" ON "user_behaviors"("user_id");
CREATE INDEX "user_behaviors_behavior_type_idx" ON "user_behaviors"("behavior_type");
CREATE INDEX "user_behaviors_category_idx" ON "user_behaviors"("category");
CREATE INDEX "user_behaviors_timestamp_idx" ON "user_behaviors"("timestamp");
CREATE INDEX "user_behaviors_user_id_timestamp_idx" ON "user_behaviors"("user_id", "timestamp");

-- Add foreign key constraints
ALTER TABLE "user_behaviors" ADD CONSTRAINT "user_behaviors_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "user_behaviors" ADD CONSTRAINT "user_behaviors_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
