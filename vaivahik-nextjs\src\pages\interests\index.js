import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import {
  Box,
  Container,
  Typography,
  Tabs,
  Tab,
  Card,
  CardContent,
  Grid,
  Avatar,
  Button,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  CircularProgress,
  Paper,
  Divider
} from '@mui/material';
import {
  Favorite as FavoriteIcon,
  FavoriteBorder as FavoriteBorderIcon,
  Visibility as ViewIcon,
  Message as MessageIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  CheckCircle as AcceptIcon,
  Cancel as RejectIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

export default function InterestsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(true);
  const [interests, setInterests] = useState({
    received: [],
    sent: []
  });
  const [selectedInterest, setSelectedInterest] = useState(null);
  const [responseDialog, setResponseDialog] = useState(false);
  const [responseMessage, setResponseMessage] = useState('');
  const [actionLoading, setActionLoading] = useState(false);

  useEffect(() => {
    fetchInterests();
  }, []);

  const fetchInterests = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/user/interests', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setInterests(data.data || { received: [], sent: [] });
      }
    } catch (error) {
      console.error('Error fetching interests:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInterestResponse = async (interestId, action, message = '') => {
    setActionLoading(true);
    try {
      const response = await fetch(`/api/user/interests/${interestId}/respond`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action: action, // 'accept' or 'reject'
          message: message
        })
      });

      if (response.ok) {
        await fetchInterests(); // Refresh the list
        setResponseDialog(false);
        setSelectedInterest(null);
        setResponseMessage('');
      }
    } catch (error) {
      console.error('Error responding to interest:', error);
    } finally {
      setActionLoading(false);
    }
  };

  const handleViewProfile = (userId) => {
    router.push(`/profile/${userId}`);
  };

  const handleSendMessage = (userId) => {
    router.push(`/messages?user=${userId}`);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'ACCEPTED': return 'success';
      case 'REJECTED': return 'error';
      case 'PENDING': return 'warning';
      default: return 'default';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'ACCEPTED': return 'Accepted';
      case 'REJECTED': return 'Declined';
      case 'PENDING': return 'Pending';
      default: return 'Unknown';
    }
  };

  const InterestCard = ({ interest, type }) => (
    <Card sx={{ mb: 2, '&:hover': { boxShadow: 4 } }}>
      <CardContent>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} sm={2}>
            <Avatar
              src={interest.user?.profilePicture}
              sx={{ width: 60, height: 60, mx: 'auto' }}
            >
              <PersonIcon />
            </Avatar>
          </Grid>
          
          <Grid item xs={12} sm={6}>
            <Typography variant="h6" gutterBottom>
              {interest.user?.firstName} {interest.user?.lastName}
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              {interest.user?.age} years • {interest.user?.location}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {interest.user?.occupation}
            </Typography>
            {interest.message && (
              <Typography variant="body2" sx={{ mt: 1, fontStyle: 'italic' }}>
                "{interest.message}"
              </Typography>
            )}
          </Grid>

          <Grid item xs={12} sm={2}>
            <Box sx={{ textAlign: 'center' }}>
              <Chip
                label={getStatusText(interest.status)}
                color={getStatusColor(interest.status)}
                size="small"
                sx={{ mb: 1 }}
              />
              <Typography variant="caption" display="block" color="text.secondary">
                <ScheduleIcon sx={{ fontSize: 12, mr: 0.5 }} />
                {format(new Date(interest.createdAt), 'MMM dd, yyyy')}
              </Typography>
            </Box>
          </Grid>

          <Grid item xs={12} sm={2}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              <Button
                size="small"
                variant="outlined"
                startIcon={<ViewIcon />}
                onClick={() => handleViewProfile(interest.user?.id)}
                fullWidth
              >
                View
              </Button>
              
              {type === 'received' && interest.status === 'PENDING' && (
                <>
                  <Button
                    size="small"
                    variant="contained"
                    color="success"
                    startIcon={<AcceptIcon />}
                    onClick={() => {
                      setSelectedInterest(interest);
                      setResponseDialog(true);
                    }}
                    fullWidth
                  >
                    Accept
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    color="error"
                    startIcon={<RejectIcon />}
                    onClick={() => handleInterestResponse(interest.id, 'reject')}
                    fullWidth
                  >
                    Decline
                  </Button>
                </>
              )}
              
              {interest.status === 'ACCEPTED' && (
                <Button
                  size="small"
                  variant="contained"
                  startIcon={<MessageIcon />}
                  onClick={() => handleSendMessage(interest.user?.id)}
                  fullWidth
                >
                  Message
                </Button>
              )}
            </Box>
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading your interests...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        <FavoriteIcon sx={{ mr: 2, verticalAlign: 'middle' }} />
        Interests
      </Typography>
      
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          variant="fullWidth"
        >
          <Tab 
            label={`Received (${interests.received?.length || 0})`}
            icon={<FavoriteBorderIcon />}
          />
          <Tab 
            label={`Sent (${interests.sent?.length || 0})`}
            icon={<FavoriteIcon />}
          />
        </Tabs>
      </Paper>

      {/* Received Interests Tab */}
      {activeTab === 0 && (
        <Box>
          {interests.received?.length > 0 ? (
            interests.received.map((interest) => (
              <InterestCard
                key={interest.id}
                interest={interest}
                type="received"
              />
            ))
          ) : (
            <Alert severity="info">
              No interests received yet. Complete your profile to attract more interest!
            </Alert>
          )}
        </Box>
      )}

      {/* Sent Interests Tab */}
      {activeTab === 1 && (
        <Box>
          {interests.sent?.length > 0 ? (
            interests.sent.map((interest) => (
              <InterestCard
                key={interest.id}
                interest={interest}
                type="sent"
              />
            ))
          ) : (
            <Alert severity="info">
              You haven't sent any interests yet. Start browsing profiles to send interests!
            </Alert>
          )}
        </Box>
      )}

      {/* Response Dialog */}
      <Dialog
        open={responseDialog}
        onClose={() => setResponseDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Accept Interest from {selectedInterest?.user?.firstName}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            You can send a personal message along with your acceptance:
          </Typography>
          <TextField
            fullWidth
            multiline
            rows={3}
            placeholder="Write a personal message (optional)..."
            value={responseMessage}
            onChange={(e) => setResponseMessage(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setResponseDialog(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={() => handleInterestResponse(selectedInterest?.id, 'accept', responseMessage)}
            disabled={actionLoading}
          >
            {actionLoading ? <CircularProgress size={20} /> : 'Accept Interest'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
}
