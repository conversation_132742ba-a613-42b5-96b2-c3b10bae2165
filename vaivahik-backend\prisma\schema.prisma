// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  // Note: For efficient geospatial queries later, consider enabling the PostGIS extension
  // extensions = [postgis] // Requires enabling PostGIS on your PostgreSQL server
}

// Enum for photo visibility
enum PhotoVisibility {
  PUBLIC
  PAID
  CONNECTIONS_ONLY
}

// Enum for Photo Moderation Status
enum PhotoStatus {
  PENDING   // Newly uploaded, awaiting review
  APPROVED  // Visible according to visibility rules
  REJECTED  // Not visible, potentially deleted later
}

// Enum for Message Types
enum MessageType {
  TEXT
  IMAGE
  FILE
  AUDIO
  VIDEO
  SYSTEM
}

// Enums for behavior tracking
enum InteractionType {
  VIEW
  LIKE
  SHORTLIST
  CONTACT_REQUESTED
  CONTACT_VIEWED
  CONTACT_ACCEPTED
  CONTACT_REJECTED
  CHAT_INITIATED
  CHAT_RESPONDED
  REPORT
  BLOCK
  IGNORE
}

enum FeedbackType {
  AFTER_VIEW
  AFTER_CONTACT
  AFTER_CHAT
  AFTER_MEETING
  GENERAL
  REASON_FOR_REJECT
  REASON_FOR_ACCEPT
}

enum SuccessStoryStatus {
  MATCHED
  TALKING
  MET
  ENGAGED
  MARRIED
}

// ------------------- User Model (Core Details) -------------------
model User {
  id            String    @id @default(cuid())
  phone         String    @unique
  email         String?   @unique
  password      String?
  isVerified    Boolean   @default(false)
  profileStatus String    @default("INCOMPLETE") // INCOMPLETE, PENDING_APPROVAL, ACTIVE, SUSPENDED, INACTIVE
  isPremium     Boolean   @default(false)

  // Premium and feature usage fields
  isIncognito   Boolean   @default(false) @map("is_incognito")
  boostedUntil  DateTime? @map("boosted_until")
  messagesSent  Int       @default(0) @map("messages_sent")

  // FCM tokens for push notifications
  fcmTokens     String[]  @default([]) @map("fcm_tokens")

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  // --- Relationships ---
  profile       Profile?    // One-to-one with Profile
  photos        Photo[]     // One-to-many with Photo
  preference    Preference? // One-to-one with Preference
  verificationDocuments VerificationDocument[] // One-to-many with VerificationDocument
  notifications Notification[] // One-to-many with Notification

  // Match relationships
  matchesAsUser1 Match[] @relation("UserMatches1")
  matchesAsUser2 Match[] @relation("UserMatches2")

  // Report relationships
  reportsSubmitted Report[] @relation("UserReports")
  reportsReceived  Report[] @relation("UserReported")

  // Subscription relationship
  subscriptions    Subscription[]

  // Payment relationship
  paymentOrders    PaymentOrder[] @relation("UserPaymentOrders")

  // Usage tracking relationships
  profilesViewed   ProfileView[] @relation("ViewerRelation")
  profileViewers   ProfileView[] @relation("ViewedRelation")
  matchViews       MatchView[]

  // Message relationships
  sentMessages     Message[]     @relation("SentMessages")
  receivedMessages Message[]     @relation("ReceivedMessages")

  // Conversation relationships
  conversationsAsUser1 Conversation[] @relation("User1Conversations")
  conversationsAsUser2 Conversation[] @relation("User2Conversations")

  // Behavior tracking relationships
  interactions        UserInteraction[] @relation("UserInteractions")
  targetInteractions  UserInteraction[] @relation("TargetUserInteractions")
  feedback            UserFeedback[]    @relation("UserFeedback")
  targetFeedback      UserFeedback[]    @relation("TargetUserFeedback")
  successStories1     SuccessStory[]    @relation("UserSuccessStories1")
  successStories2     SuccessStory[]    @relation("UserSuccessStories2")
  preferenceHistory   UserPreferenceHistory[]
  matchScores         MatchScore[]      @relation("UserMatchScores")
  targetMatchScores   MatchScore[]      @relation("TargetUserMatchScores")

  // Premium features relationships
  biodatas           UserBiodata[]
  spotlights         UserSpotlight[]

  // Referral relationships
  referrals          Referral[]     @relation("UserReferrals")

  // Admin audit logs
  adminAuditLogs     AdminAuditLog[]

  // Topic subscription relationships
  topicSubscriptions TopicSubscription[]

  // Contact access relationships
  contactAccessGiven    ContactAccessLog[] @relation("ContactOwner")
  contactAccessReceived ContactAccessLog[] @relation("ContactAccessor")

  // Security relationships
  securityLogs          SecurityLog[]

  // AI decision logs
  aiDecisionLogs        AIDecisionLog[]

  @@index([profileStatus]) // Index for filtering by profile status
  @@index([isPremium]) // Index for filtering premium users
  @@index([isVerified]) // Index for filtering verified users
  @@index([createdAt]) // Index for sorting by creation date
  @@map("users")
}

// ------------------- Profile Model (Detailed Info - UPDATED) -------------------
model Profile {
  id            String    @id @default(cuid())
  userId        String    @unique @map("user_id") // Foreign key referencing User
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade) // Define relation

  // --- Basic Profile Details ---
  fullName        String?
  gender          String?   // Consider Enum: enum Gender { MALE FEMALE OTHER }
  profileFor      String?   @map("profile_for") // Self, Son, Daughter, Brother, Sister, etc.
  dateOfBirth     DateTime? @map("birth_date")
  birthTime       String?   @map("birth_time")
  birthPlace      String?   @map("birth_place")

  // --- Community / Background ---  (ADDED/UPDATED Fields)
  religion        String?                 // Added
  caste           String?                 // Added
  subCaste        String? @map("sub_caste") // Specific Maratha sub-caste
  gotra           String?                 // Ancestral lineage
  kul             String?                 // Family/clan name
  motherTongue    String? @map("mother_tongue") // Added + mapped
  marathiProficiency String? @map("marathi_proficiency") // Fluency in Marathi language

  // --- Lifestyle & Physical --- (ADDED/UPDATED Fields)
  height          String?
  diet            String?                 // Added
  maritalStatus   String? @map("marital_status") // Added + mapped // Consider Enum

  // --- Location --- (ADDED Fields)
  city            String?
  state           String?                 // Added
  country         String?                 // Added
  nativePlace     String?   @map("native_place")
  nativeDistrict  String?   @map("native_district") // District in Maharashtra
  maharashtrianOrigin Boolean? @map("maharashtrian_origin") // Is family originally from Maharashtra
  latitude        Float?    // Store latitude
  longitude       Float?    // Store longitude

  // --- Education & Profession --- (UPDATED Fields)
  highestEducation String? @map("highest_education") // Renamed from 'education'
  occupation      String?

  // --- Financial --- (UPDATED Fields)
  annualIncome    String? @map("annual_income") // Renamed from 'incomeRange'

  // --- Family Details --- (ADDED/UPDATED Fields)
  familyType        String? @map("family_type") // Added
  familyStatus      String? @map("family_status") // Added
  fatherName        String? @map("father_name") // Kept original name, but added occupation below
  fatherOccupation  String? @map("father_occupation") // Added
  motherName        String? @map("mother_name") // Kept original name, but added occupation below
  motherOccupation  String? @map("mother_occupation") // Added
  uncleName         String? @map("uncle_name")
  siblings          String? // Changed from total/married Int? to String? for flexibility
  // totalSiblings     Int?    @map("total_siblings") // Removed/Replaced
  // marriedSiblings   Int?    @map("married_siblings") // Removed/Replaced
  familyContact     String? @map("family_contact")

  // --- About --- (ADDED Fields)
  aboutMe           String? @map("about_me") @db.Text // Added, use Text for longer content
  partnerPreferences String? @map("partner_preferences") @db.Text // Added

  // --- Hobbies & Interests --- (ADDED Fields)
  hobbies           String? @db.Text // Comma-separated list or JSON string of hobbies
  interests         String? @db.Text // Comma-separated list or JSON string of interests

  // --- Privacy Settings --- (NEW Fields)
  displayNamePreference String @default("FIRST_NAME") @map("display_name_preference") // FULL_NAME, FIRST_NAME, PROFILE_ID, ANONYMOUS
  showNameInNotifications Boolean @default(true) @map("show_name_in_notifications")
  showNameInSearch Boolean @default(true) @map("show_name_in_search")
  showNameInMatches Boolean @default(true) @map("show_name_in_matches")
  showNameInMessages Boolean @default(true) @map("show_name_in_messages")
  allowProfileViews Boolean @default(true) @map("allow_profile_views")
  showOnlineStatus Boolean @default(false) @map("show_online_status")
  showLastSeen Boolean @default(false) @map("show_last_seen")
  allowDirectMessages Boolean @default(true) @map("allow_direct_messages")
  showContactInfo Boolean @default(false) @map("show_contact_info") // Only for premium users

  // --- Contact & Calling Privacy --- (NEW Fields)
  allowDirectCalls Boolean @default(true) @map("allow_direct_calls") // Allow others to call via revealed contact
  contactRevealPreference String @default("PREMIUM_ONLY") @map("contact_reveal_preference") // PREMIUM_ONLY, MUTUAL_INTEREST, ACCEPTED_INTEREST, NEVER
  requireMutualInterest Boolean @default(true) @map("require_mutual_interest") // Require mutual interest before contact reveal
  callAvailability String @default("ANYTIME") @map("call_availability") // ANYTIME, BUSINESS_HOURS, EVENING_ONLY, WEEKEND_ONLY

  // --- Timestamps ---
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @default(now()) @updatedAt @map("updated_at")

  // Indexes for frequently queried fields
  @@index([gender]) // Index for filtering by gender
  @@index([dateOfBirth]) // Index for age-based filtering
  @@index([city]) // Index for location-based filtering
  @@index([religion, caste, subCaste]) // Composite index for community filtering
  @@index([maritalStatus]) // Index for marital status filtering
  @@index([occupation]) // Index for occupation-based filtering
  @@index([height]) // Index for height-based filtering
  @@map("profiles")
}

// ------------------- Photo Model -------------------
model Photo {
  id            String          @id @default(cuid())
  userId        String          @map("user_id")
  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  url           String          // URL path to the stored photo
  visibility    PhotoVisibility @default(PUBLIC) // Visibility setting for this photo
  status        PhotoStatus     @default(PENDING) // Moderation Status
  isProfilePic  Boolean         @default(false) @map("is_profile_pic")
  uploadedAt    DateTime        @default(now()) @map("uploaded_at")

  // AI Moderation fields
  aiFlags       String?         @map("ai_flags") // Comma-separated flags from AI analysis
  aiConfidence  Float?          @map("ai_confidence") // Confidence score from AI (0-100)

  // Relationships
  moderationLogs ModerationLog[]

  // Indexes for frequently queried fields
  @@index([userId]) // Index for filtering by user
  @@index([isProfilePic]) // Index for filtering profile pictures
  @@index([visibility, status]) // Composite index for visibility and status filtering
  @@map("photos")
}


// ------------------- Admin Model -------------------
model Admin {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  role      String   @default("ADMIN") // e.g., ADMIN, SUPER_ADMIN
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  // Relations
  auditLogs AdminAuditLog[]

  @@map("admins")
}

// Admin Audit Log model for tracking admin actions
model AdminAuditLog {
  id        String   @id @default(cuid())
  adminId   String
  userId    String?
  action    String
  details   Json?
  createdAt DateTime @default(now())

  // Relations
  admin     Admin    @relation(fields: [adminId], references: [id])
  user      User?    @relation(fields: [userId], references: [id])

  @@map("admin_audit_logs")
}

// AI Decision Log model for tracking AI algorithm decisions
model AIDecisionLog {
  id              String   @id @default(cuid())
  algorithm       String
  userId          String
  inputData       String
  result          String
  metadata        String?
  timestamp       DateTime @default(now())
  sessionId       String?
  requestId       String?
  executionTimeMs Int?
  success         Boolean  @default(true)
  errorMessage    String?

  // Relations
  user            User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([algorithm])
  @@index([timestamp])
  @@index([sessionId])
  @@map("ai_decision_logs")
}

// ------------------- Preference Model -------------------
model Preference {
  id               String    @id @default(cuid())
  userId           String    @unique @map("user_id")
  user             User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Age range
  ageMin           Int?      @map("age_min")
  ageMax           Int?      @map("age_max")

  // Height range
  heightMin        String?   @map("height_min")
  heightMax        String?   @map("height_max")

  // Education & Career
  educationLevel   String[]  @map("education_level")  // Array (multiple degrees allowed)
  occupations      String[]                           // Array (multiple jobs allowed)
  incomeMin        String?   @map("income_min")        // Minimum expected income

  // Location preferences
  preferredCities  String[]  @map("preferred_cities")
  preferredStates  String[]  @map("preferred_states")

  // Maratha specific
  acceptSubCastes  String[]  @map("accept_sub_castes") // Accepted sub-castes
  gotraPreference  String?   @map("gotra_preference")  // Specific Gotra if needed

  // Lifestyle
  dietPreference   String?   @map("diet_preference")   // Veg, Non-Veg, Eggetarian etc.

  // Hobbies & Interests preferences
  hobbiesPreference String?  @map("hobbies_preference") @db.Text // Preferred hobbies in partner
  interestsPreference String? @map("interests_preference") @db.Text // Preferred interests in partner

  // Other preferences
  otherPreferences String?   @map("other_preferences") @db.Text

  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("preferences")
}

// ------------------- Match Model -------------------
model Match {
  id                 String    @id @default(cuid())

  // The two users in this match
  user1Id            String    @map("user1_id")
  user1              User      @relation("UserMatches1", fields: [user1Id], references: [id], onDelete: Cascade)
  user2Id            String    @map("user2_id")
  user2              User      @relation("UserMatches2", fields: [user2Id], references: [id], onDelete: Cascade)

  // Match status
  status             String    @default("PENDING") // PENDING, ACCEPTED, REJECTED, CANCELLED
  initiatedBy        String    @map("initiated_by") // user1_id or user2_id

  // AI matching score
  compatibilityScore Float?    @map("compatibility_score")
  matchReason        String?   @map("match_reason") @db.Text // Why AI suggested this match

  // Communication
  lastMessageAt      DateTime? @map("last_message_at")

  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @default(now()) @updatedAt @map("updated_at")

  @@unique([user1Id, user2Id])
  @@map("matches")
}

// ------------------- Report Model -------------------
model Report {
  id            String    @id @default(cuid())
  reporterId    String    @map("reporter_id")
  reporter      User      @relation("UserReports", fields: [reporterId], references: [id], onDelete: Cascade)
  reportedId    String    @map("reported_id")
  reported      User      @relation("UserReported", fields: [reportedId], references: [id], onDelete: Cascade)

  reason        String    // Predefined reason code
  details       String?   @db.Text // Additional details provided by reporter
  status        String    @default("PENDING") // PENDING, REVIEWED, DISMISSED, ACTIONED
  adminNotes    String?   @map("admin_notes") @db.Text // Notes from admin review

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("reports")
}

// ------------------- Subscription Model -------------------
model Subscription {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  planType      String    @map("plan_type") // BASIC, PREMIUM, VIP
  amount        Float
  currency      String    @default("INR")
  startDate     DateTime  @map("start_date")
  endDate       DateTime  @map("end_date")
  isActive      Boolean   @default(true) @map("is_active")
  autoRenew     Boolean   @default(false) @map("auto_renew")

  // Payment details
  paymentMethod String?   @map("payment_method")
  transactionId String?   @map("transaction_id")
  paymentOrderId String? @unique @map("payment_order_id")
  paymentOrder  PaymentOrder? @relation("PaymentOrderSubscription", fields: [paymentOrderId], references: [id])

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("subscriptions")
}

// ------------------- VerificationDocument Model -------------------
enum DocumentType {
  AADHAR_CARD
  PAN_CARD
  VOTER_ID
  PASSPORT
  DRIVING_LICENSE
  OTHER
}

enum DocumentStatus {
  PENDING_REVIEW
  APPROVED
  REJECTED
}

model VerificationDocument {
  id            String        @id @default(cuid())
  userId        String        @map("user_id")
  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)

  type          DocumentType
  url           String        // URL path to the stored document
  filename      String        // Original filename
  filesize      Int           // Size in bytes
  mimeType      String        @map("mime_type")

  status        DocumentStatus @default(PENDING_REVIEW)
  adminNotes    String?       @map("admin_notes") @db.Text

  uploadedAt    DateTime      @default(now()) @map("uploaded_at")
  reviewedAt    DateTime?     @map("reviewed_at")
  reviewedBy    String?       @map("reviewed_by") // Admin ID who reviewed this document

  @@map("verification_documents")
}

// ------------------- Notification Models -------------------
model Notification {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  title         String
  body          String    // Changed from 'message' to match FCM terminology
  imageUrl      String?   @map("image_url")
  data          Json?     // Additional data for the notification (type, actionUrl, etc.)

  isRead        Boolean   @default(false) @map("is_read")
  readAt        DateTime? @map("read_at")
  sentViaFCM    Boolean   @default(false) @map("sent_via_fcm")

  createdAt     DateTime  @default(now()) @map("created_at")

  // Indexes for frequently queried fields
  @@index([userId]) // Index for filtering by user
  @@index([isRead]) // Index for filtering unread notifications
  @@index([createdAt]) // Index for sorting by creation date
  @@map("notifications")
}

// Topic-based notifications
model TopicNotification {
  id            String    @id @default(cuid())
  topic         String    // The topic this notification was sent to

  title         String
  body          String
  imageUrl      String?   @map("image_url")
  data          Json?     // Additional data for the notification

  sentAt        DateTime  @default(now()) @map("sent_at")

  @@map("topic_notifications")
}

// User topic subscriptions
model TopicSubscription {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  topic         String    // The topic the user is subscribed to
  subscribedAt  DateTime  @default(now()) @map("subscribed_at")

  @@unique([userId, topic])
  @@map("topic_subscriptions")
}

// Scheduled notifications
model ScheduledNotification {
  id              String    @id @default(cuid())
  notificationType String    @map("notification_type") // e.g., 'promotional', 'newMatch'

  targetType      String    @map("target_type") // USER, TOPIC, ALL_USERS
  targetId        String?   @map("target_id") // User ID or topic name (null for ALL_USERS)

  data            Json      // Data for the notification template
  scheduledFor    DateTime  @map("scheduled_for")
  sentAt          DateTime? @map("sent_at")

  status          String    @default("SCHEDULED") // SCHEDULED, SENT, FAILED, CANCELLED
  statusDetails   String?   @map("status_details") @db.Text

  createdAt       DateTime  @default(now()) @map("created_at")
  createdBy       String?   @map("created_by") // Admin ID who created this scheduled notification

  @@map("scheduled_notifications")
}

// ------------------- Usage Tracking Models -------------------

// Track profile views
model ProfileView {
  id            String    @id @default(cuid())
  viewerId      String    @map("viewer_id")
  viewer        User      @relation("ViewerRelation", fields: [viewerId], references: [id], onDelete: Cascade)
  viewedId      String    @map("viewed_id")
  viewed        User      @relation("ViewedRelation", fields: [viewedId], references: [id], onDelete: Cascade)
  viewedAt      DateTime  @default(now()) @map("viewed_at")

  @@map("profile_views")
}

// Track match views
model MatchView {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  viewedAt      DateTime  @default(now()) @map("viewed_at")

  @@map("match_views")
}

// ------------------- Conversation Model -------------------
model Conversation {
  id            String    @id @default(cuid())

  // The two users in this conversation
  user1Id       String    @map("user1_id")
  user1         User      @relation("User1Conversations", fields: [user1Id], references: [id], onDelete: Cascade)
  user2Id       String    @map("user2_id")
  user2         User      @relation("User2Conversations", fields: [user2Id], references: [id], onDelete: Cascade)

  // Conversation status
  lastMessageAt DateTime? @map("last_message_at")
  isActive      Boolean   @default(true) @map("is_active")

  // Timestamps
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  // Relationships
  messages      Message[]

  @@unique([user1Id, user2Id])
  @@index([user1Id])
  @@index([user2Id])
  @@index([lastMessageAt]) // Index for sorting by last message time
  @@index([isActive]) // Index for filtering active conversations
  @@map("conversations")
}

// ------------------- Message Model -------------------
model Message {
  id            String      @id @default(cuid())

  // Sender and receiver
  senderId      String      @map("sender_id")
  sender        User        @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  receiverId    String      @map("receiver_id")
  receiver      User        @relation("ReceivedMessages", fields: [receiverId], references: [id], onDelete: Cascade)

  // Conversation relationship
  conversationId String?    @map("conversation_id")
  conversation   Conversation? @relation(fields: [conversationId], references: [id], onDelete: SetNull)

  // Message content
  content       String      @db.Text
  messageType   MessageType @default(TEXT) @map("message_type")
  metadata      String?     @db.Text // JSON string with additional data based on message type

  // Message status
  isRead        Boolean     @default(false) @map("is_read")
  readAt        DateTime?   @map("read_at")
  sentAt        DateTime    @default(now()) @map("sent_at")

  // Moderation fields
  isModerated   Boolean     @default(false) @map("is_moderated")
  moderationStatus String?  @map("moderation_status") // APPROVED, REJECTED, PENDING
  moderatedContent String?  @db.Text @map("moderated_content") // Content after moderation (e.g., with profanity masked)
  moderationFlags String?   @map("moderation_flags") // Comma-separated list of flags (profanity, contact_info, spam, etc.)

  @@index([conversationId])
  @@index([senderId])
  @@index([receiverId])
  @@index([sentAt]) // Index for sorting by sent time
  @@index([isRead]) // Index for filtering unread messages
  @@index([messageType]) // Index for filtering by message type
  @@map("messages")
}

// ------------------- Subscription Plan Model -------------------
model SubscriptionPlan {
  id            String    @id @default(cuid())
  name          String
  planType      String    @map("plan_type") // BASIC, PREMIUM, VIP
  price         Float
  currency      String    @default("INR")
  duration      Int       // Duration in days
  description   String?
  features      String    @db.Text // JSON array of features
  isActive      Boolean   @default(true) @map("is_active")

  // Relationships
  featureAccess FeatureAccess[]

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("subscription_plans")
}

// ------------------- Payment Order Model -------------------
model PaymentOrder {
  id                String    @id @default(cuid())
  orderId           String    @unique @map("order_id") // Razorpay order ID
  userId            String    @map("user_id")
  user              User      @relation("UserPaymentOrders", fields: [userId], references: [id], onDelete: Cascade)

  // Order details
  amount            Float     // Amount in rupees
  currency          String    @default("INR")
  status            String    @default("CREATED") // CREATED, COMPLETED, FAILED, CANCELLED

  // Subscription details (if applicable)
  planType          String?   @map("plan_type") // PREMIUM, etc.
  planDuration      String?   @map("plan_duration") // monthly, quarterly, annual

  // Feature details (if applicable)
  featureType       String?   @map("feature_type") // PROFILE_BOOST, SUPER_LIKES, etc.
  quantity          Int?      // For feature purchases

  // Razorpay details
  razorpayOrderId   String    @map("razorpay_order_id")
  razorpayPaymentId String?   @map("razorpay_payment_id")
  razorpaySignature String?   @map("razorpay_signature")
  receipt           String?   // Receipt number

  // Failure details
  failureReason     String?   @map("failure_reason")

  // Relationships
  subscription      Subscription? @relation("PaymentOrderSubscription")

  // Timestamps
  createdAt         DateTime  @default(now()) @map("created_at")
  completedAt       DateTime? @map("completed_at")

  @@map("payment_orders")
}

// ------------------- Contact Access Log Model -------------------
model ContactAccessLog {
  id                String    @id @default(cuid())
  accessorId        String    @map("accessor_id")
  accessor          User      @relation("ContactAccessor", fields: [accessorId], references: [id], onDelete: Cascade)
  contactOwnerId    String    @map("contact_owner_id")
  contactOwner      User      @relation("ContactOwner", fields: [contactOwnerId], references: [id], onDelete: Cascade)

  // Access details
  accessType        String    @default("CONTACT_REVEAL") // CONTACT_REVEAL, CALL_ATTEMPT, CALL_CONNECTED
  contactNumber     String?   @map("contact_number") // The revealed contact number
  accessReason      String?   @map("access_reason") // PREMIUM_SUBSCRIPTION, MUTUAL_INTEREST, etc.

  // Premium tracking
  isPremiumAccess   Boolean   @default(false) @map("is_premium_access")
  featurePurchaseId String?   @map("feature_purchase_id") // Link to payment if per-contact purchase

  // Call tracking (for analytics)
  callDuration      Int?      @map("call_duration") // Duration in seconds (if trackable)
  callStatus        String?   @map("call_status") // INITIATED, CONNECTED, FAILED, BUSY

  // Metadata
  platform          String?   // WEB, ANDROID, IOS
  userAgent         String?   @map("user_agent")
  ipAddress         String?   @map("ip_address")

  // Timestamps
  accessedAt        DateTime  @default(now()) @map("accessed_at")

  @@map("contact_access_logs")
}

// ------------------- Security Log Model -------------------
model SecurityLog {
  id            String    @id @default(cuid())
  userId        String?   @map("user_id")
  user          User?     @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Event details
  eventType     String    @map("event_type") // CONTACT_ACCESS_BLOCKED, HIGH_RISK_DETECTED, etc.
  riskScore     Int       @default(0) @map("risk_score")
  securityFlags String?   @map("security_flags") // Comma-separated flags
  details       String?   // JSON string with additional details

  // Request metadata
  ipAddress     String?   @map("ip_address")
  userAgent     String?   @map("user_agent")
  platform      String?   // WEB, ANDROID, IOS

  // Timestamps
  createdAt     DateTime  @default(now()) @map("created_at")

  @@map("security_logs")
}

// ------------------- Feature Configuration Models -------------------

// Features that can be configured
model Feature {
  id            String    @id @default(cuid())
  name          String    @unique
  displayName   String    @map("display_name")
  description   String?
  category      String    // BASIC, COMMUNICATION, MATCHING, PREMIUM
  isActive      Boolean   @default(true) @map("is_active")

  // Relationships
  accessRules   FeatureAccess[]

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("features")
}

// Access rules for features based on user tier
model FeatureAccess {
  id                String    @id @default(cuid())
  featureId         String    @map("feature_id")
  feature           Feature   @relation(fields: [featureId], references: [id], onDelete: Cascade)

  // User tier this rule applies to
  userTier          String    @map("user_tier") // BASIC, VERIFIED, PREMIUM

  // Subscription plan this rule applies to (optional, for premium tier)
  subscriptionPlanId String?  @map("subscription_plan_id")
  subscriptionPlan   SubscriptionPlan? @relation(fields: [subscriptionPlanId], references: [id], onDelete: SetNull)

  // Access configuration
  isEnabled         Boolean   @default(false) @map("is_enabled")
  dailyLimit        Int?      @map("daily_limit")
  totalLimit        Int?      @map("total_limit")
  limitPeriod       String?   @map("limit_period") // DAILY, WEEKLY, MONTHLY, TOTAL

  // For search features
  allowedFilters    String?   @map("allowed_filters") @db.Text // JSON array of allowed filters

  // Upgrade messaging
  upgradeMessage    String?   @map("upgrade_message")

  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @default(now()) @updatedAt @map("updated_at")

  @@unique([featureId, userTier, subscriptionPlanId])
  @@map("feature_access")
}

// System configuration for the application
model SystemConfig {
  id            String    @id @default(cuid())
  configKey     String    @unique @map("config_key")
  configValue   String    @map("config_value") @db.Text
  description   String?
  isActive      Boolean   @default(true) @map("is_active")

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("system_config")
}

// ------------------- Moderation Log Model -------------------
model ModerationLog {
  id            String    @id @default(cuid())

  // Content type and ID
  contentType   String    @map("content_type") // PHOTO, MESSAGE, PROFILE, etc.
  contentId     String?   @map("content_id") // ID of the moderated content

  // Photo relationship (for backward compatibility)
  photoId       String?   @map("photo_id")
  photo         Photo?    @relation(fields: [photoId], references: [id], onDelete: SetNull)

  // User who created the content
  userId        String?   @map("user_id")

  // Moderation details
  decision      String    @map("decision") // APPROVED, REJECTED, PENDING
  flags         String?   @map("flags") // Comma-separated flags from analysis
  confidence    Float?    @map("confidence") // Confidence score (0-100)
  details       String?   @db.Text // JSON string with detailed analysis

  // AI-specific fields (for photo moderation)
  aiDecision    String?   @map("ai_decision") // APPROVED, REJECTED, PENDING
  aiFlags       String?   @map("ai_flags") // Comma-separated flags from AI analysis
  aiConfidence  Float?    @map("ai_confidence") // Confidence score from AI (0-100)

  // Admin action
  reviewedBy    String?   @map("reviewed_by") // Admin ID who reviewed this
  reviewedAt    DateTime? @map("reviewed_at")
  adminNotes    String?   @map("admin_notes")

  createdAt     DateTime  @default(now()) @map("created_at")

  @@index([photoId])
  @@index([contentType, contentId])
  @@index([userId])
  @@map("moderation_logs")
}

// --- Add other models later as needed ---
// model Transaction { ... }
// model BlogPost { ... }
// model SuccessStory { ... }
// model Setting { ... }


// Preference Configuration Schema Additions

// Preference Category Model
model PreferenceCategory {
  id            String    @id @default(cuid())
  name          String    @unique // Internal name (e.g., "physical_attributes")
  displayName   String    @map("display_name") // Display name (e.g., "Physical Attributes")
  description   String?
  displayOrder  Int       @default(0) @map("display_order") // Order for display in UI
  icon          String?   // Icon name or path
  isActive      Boolean   @default(true) @map("is_active")
  isRequired    Boolean   @default(false) @map("is_required") // Whether this category must be filled

  // Relationships
  fields        PreferenceField[]

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")
  createdBy     String?   @map("created_by") // Admin ID who created this
  updatedBy     String?   @map("updated_by") // Admin ID who last updated this

  @@map("preference_categories")
}

// Preference Field Model
model PreferenceField {
  id              String    @id @default(cuid())
  name            String    // Internal name (e.g., "height")
  displayName     String    @map("display_name") // Display name (e.g., "Height")
  description     String?
  fieldType       String    @map("field_type") // TEXT, NUMBER, RANGE, SELECT, MULTI_SELECT, BOOLEAN
  displayOrder    Int       @default(0) @map("display_order") // Order within category
  isActive        Boolean   @default(true) @map("is_active")
  isRequired      Boolean   @default(false) @map("is_required") // Whether this field must be filled
  isSearchable    Boolean   @default(true) @map("is_searchable") // Whether this field can be used in search
  isMatchCriteria Boolean   @default(true) @map("is_match_criteria") // Whether this field is used in matching
  defaultValue    String?   @map("default_value") // Default value as JSON string
  validationRules String?   @map("validation_rules") // JSON string with validation rules

  // For range fields
  minValue        Float?    @map("min_value")
  maxValue        Float?    @map("max_value")
  stepValue       Float?    @map("step_value")

  // Relationships
  categoryId      String    @map("category_id")
  category        PreferenceCategory @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  options         PreferenceOption[]
  importanceSettings PreferenceImportance[]

  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at")
  createdBy       String?   @map("created_by") // Admin ID who created this
  updatedBy       String?   @map("updated_by") // Admin ID who last updated this

  @@unique([categoryId, name])
  @@map("preference_fields")
}

// Preference Option Model (for SELECT and MULTI_SELECT fields)
model PreferenceOption {
  id              String    @id @default(cuid())
  value           String    // Internal value (e.g., "doctor")
  displayText     String    @map("display_text") // Display text (e.g., "Doctor")
  description     String?
  displayOrder    Int       @default(0) @map("display_order")
  isActive        Boolean   @default(true) @map("is_active")

  // Relationships
  fieldId         String    @map("field_id")
  field           PreferenceField @relation(fields: [fieldId], references: [id], onDelete: Cascade)

  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at")
  createdBy       String?   @map("created_by")
  updatedBy       String?   @map("updated_by")

  @@unique([fieldId, value])
  @@map("preference_options")
}

// Preference Importance Model (for weighting in matching algorithm)
model PreferenceImportance {
  id              String    @id @default(cuid())
  importanceLevel Float     @default(1.0) @map("importance_level") // Weight factor (0.0 to 10.0)
  description     String?
  isActive        Boolean   @default(true) @map("is_active")

  // Relationships
  fieldId         String    @map("field_id")
  field           PreferenceField @relation(fields: [fieldId], references: [id], onDelete: Cascade)

  // For gender-specific importance
  gender          String?   // MALE, FEMALE, ALL

  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @default(now()) @updatedAt @map("updated_at")
  createdBy       String?   @map("created_by")
  updatedBy       String?   @map("updated_by")

  @@unique([fieldId, gender])
  @@map("preference_importance")
}

// Update existing Preference model to reference the new configuration
// Note: This is a comment about the existing Preference model
// The actual model is defined above

// System Configuration for Default Preferences
// This will be used to store default preference values
// Note: This is a comment about the existing SystemConfig model
// The actual model is defined above

// ------------------- Algorithm Settings Models -------------------

// Algorithm Settings Model
model AlgorithmSetting {
  id            String    @id @default(cuid())
  category      String    // GENERAL, WEIGHTS, THRESHOLDS, ADVANCED
  key           String    // e.g., ageWeight, minimumMatchScore
  value         String    // Stored as string, converted based on dataType
  dataType      String    @default("STRING") @map("data_type") // STRING, NUMBER, BOOLEAN, JSON
  description   String?
  isActive      Boolean   @default(true) @map("is_active")

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@unique([category, key])
  @@map("algorithm_settings")
}

// Algorithm Models (for two-tower model and other ML models)
model AlgorithmModel {
  id            String    @id @default(cuid())
  name          String    // e.g., TwoTowerModel, ContentBasedModel
  version       String    // e.g., 1.0.0
  type          String    // e.g., TWO_TOWER, CONTENT_BASED, HYBRID
  description   String?
  modelPath     String?   @map("model_path") // Path to the model file
  config        String?   // JSON string with model configuration
  metrics       String?   // JSON string with model performance metrics
  isActive      Boolean   @default(false) @map("is_active")
  isDefault     Boolean   @default(false) @map("is_default")

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@unique([name, version])
  @@map("algorithm_models")
}

// A/B Testing for Algorithm Variants
model AlgorithmABTest {
  id            String    @id @default(cuid())
  name          String    @unique
  description   String?
  variantA      String    @map("variant_a") // Reference to algorithm model or setting
  variantB      String    @map("variant_b") // Reference to algorithm model or setting
  distribution  Int       @default(50) // Percentage of users to receive variant B
  startDate     DateTime  @map("start_date")
  endDate       DateTime? @map("end_date")
  metrics       String?   // JSON string with test results
  isActive      Boolean   @default(true) @map("is_active")

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("algorithm_ab_tests")
}

// ------------------- User Behavior Tracking Models -------------------

// UserInteraction model to track all user interactions with profiles
model UserInteraction {
  id            String   @id @default(cuid())
  userId        String   // User who performed the interaction
  targetUserId  String   // User whose profile was interacted with
  interactionType InteractionType
  timestamp     DateTime @default(now())
  duration      Int?     // Duration in seconds (for profile views)
  deviceInfo    Json?    // Information about the device used
  ipAddress     String?  // IP address for security and analytics

  // Features viewed during interaction
  viewedPhotos      Boolean @default(false)
  viewedDetails     Boolean @default(false)
  viewedPreferences Boolean @default(false)
  viewedContact     Boolean @default(false)

  // Relationship with User model
  user        User    @relation("UserInteractions", fields: [userId], references: [id])
  targetUser  User    @relation("TargetUserInteractions", fields: [targetUserId], references: [id])

  @@index([userId])
  @@index([targetUserId])
  @@index([interactionType])
  @@index([timestamp])
  @@map("user_interactions")
}

// UserFeedback model to collect feedback after interactions
model UserFeedback {
  id            String   @id @default(cuid())
  userId        String   // User who provided the feedback
  targetUserId  String   // User about whom feedback was provided
  feedbackType  FeedbackType
  rating        Float    // Rating on a scale of 1-5
  comments      String?  @db.Text // Optional comments
  continueInterest Boolean @default(false) // Whether user wants to continue interaction
  timestamp     DateTime @default(now())

  // Factors liked and disliked
  factorsLiked   Json?   // Array of factors liked
  factorsDisliked Json?  // Array of factors disliked

  // Relationship with User model
  user        User    @relation("UserFeedback", fields: [userId], references: [id])
  targetUser  User    @relation("TargetUserFeedback", fields: [targetUserId], references: [id])

  @@index([userId])
  @@index([targetUserId])
  @@index([feedbackType])
  @@index([timestamp])
  @@map("user_feedback")
}

// SuccessStory model to track successful matches
model SuccessStory {
  id            String   @id @default(cuid())
  userId1       String   // First user in the match
  userId2       String   // Second user in the match
  status        SuccessStoryStatus
  storyText     String?  @db.Text // Their story
  testimonyText String?  @db.Text // Testimony about the app
  rating        Float?   // Rating of the app experience
  engagementDate DateTime?
  marriageDate  DateTime?
  photos        Json?    // Array of photo URLs
  isPublic      Boolean  @default(false) // Whether to display publicly
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Factors that contributed to success
  successFactors Json?   // Array of factors that contributed to success

  // Relationship with User model
  user1        User    @relation("UserSuccessStories1", fields: [userId1], references: [id])
  user2        User    @relation("UserSuccessStories2", fields: [userId2], references: [id])

  @@index([userId1])
  @@index([userId2])
  @@index([status])
  @@index([isPublic])
  @@map("success_stories")
}

// UserPreferenceHistory to track how preferences change over time
model UserPreferenceHistory {
  id            String   @id @default(cuid())
  userId        String
  preferenceType String   // Type of preference that changed
  oldValue      Json?    // Previous value
  newValue      Json?    // New value
  changeReason  String?  // Reason for the change
  timestamp     DateTime @default(now())

  // Relationship with User model
  user        User    @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([preferenceType])
  @@index([timestamp])
  @@map("user_preference_history")
}

// MatchScore to store historical match scores
model MatchScore {
  id            String   @id @default(cuid())
  userId        String   // User who received the match
  targetUserId  String   // User who was matched
  score         Float    // Overall match score
  factorScores  Json     // Individual factor scores
  algorithm     String   // Algorithm version used
  timestamp     DateTime @default(now())

  // Relationship with User model
  user        User    @relation("UserMatchScores", fields: [userId], references: [id])
  targetUser  User    @relation("TargetUserMatchScores", fields: [targetUserId], references: [id])

  @@index([userId])
  @@index([targetUserId])
  @@index([score])
  @@index([timestamp])
  @@map("match_scores")
}

// ------------------- Referral Program Models -------------------

// Referral Program Model
model ReferralProgram {
  id                    String      @id @default(cuid())
  name                  String
  description           String?
  status                String      @default("active") // active, inactive, scheduled
  startDate             DateTime
  endDate               DateTime?
  referrerRewardType    String      // cash, subscription_days, premium_features
  referrerRewardAmount  Float
  refereeRewardType     String      // cash, subscription_days, premium_features
  refereeRewardAmount   Float
  maxReferralsPerUser   Int?
  conversionRequirement String      @default("none") // none, profile_complete, subscription
  termsAndConditions    String?     @db.Text
  createdAt             DateTime    @default(now()) @map("created_at")
  updatedAt             DateTime    @default(now()) @updatedAt @map("updated_at")
  createdBy             String?     @map("created_by") // Admin ID who created this

  // Relationships
  referrals             Referral[]

  @@map("referral_programs")
}

// Referral Model
model Referral {
  id                  String          @id @default(cuid())
  referralProgramId   String          @map("referral_program_id")
  referralProgram     ReferralProgram @relation(fields: [referralProgramId], references: [id])
  referrerId          String          @map("referrer_id") // User who referred
  referrer            User            @relation("UserReferrals", fields: [referrerId], references: [id])
  refereeId           String?         @map("referee_id") // User who was referred (null until they sign up)
  referralCode        String          @unique
  referralLink        String          @map("referral_link")
  status              String          @default("pending") // pending, completed, rewarded
  referrerRewardStatus String         @default("pending") @map("referrer_reward_status") // pending, paid
  refereeRewardStatus  String         @default("pending") @map("referee_reward_status") // pending, paid
  conversionStatus    String          @default("none") @map("conversion_status") // none, profile_complete, subscription
  referralEmail       String?         @map("referral_email") // Email used for invitation
  referralPhone       String?         @map("referral_phone") // Phone used for invitation
  clickCount          Int             @default(0) @map("click_count")
  createdAt           DateTime        @default(now()) @map("created_at")
  updatedAt           DateTime        @default(now()) @updatedAt @map("updated_at")

  // Relationships
  rewards             ReferralReward[]

  @@index([referrerId])
  @@index([refereeId])
  @@index([referralProgramId])
  @@index([status])
  @@map("referrals")
}

// Referral Reward Transaction Model
model ReferralReward {
  id                String   @id @default(cuid())
  referralId        String   @map("referral_id") // Related referral
  referral          Referral @relation(fields: [referralId], references: [id])
  userId            String   @map("user_id") // User who received the reward
  rewardType        String   @map("reward_type") // cash, subscription_days, premium_features
  rewardAmount      Float    @map("reward_amount")
  status            String   @default("processed") // processed, pending, failed
  transactionDetails Json?    @map("transaction_details") // Additional details about the reward
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @default(now()) @updatedAt @map("updated_at")

  @@index([referralId])
  @@index([userId])
  @@index([status])
  @@map("referral_rewards")
}

// ------------------- Premium Features Models -------------------

// Biodata Template Model
model BiodataTemplate {
  id            String    @id @default(cuid())
  name          String
  description   String?
  previewImage  String    @map("preview_image")
  designFile    String    @map("design_file")
  price         Float
  discountPercent Int?    @map("discount_percent")
  discountedPrice Float?  @map("discounted_price")
  isActive      Boolean   @default(true) @map("is_active")

  // Relationships
  userBiodatas  UserBiodata[]

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("biodata_templates")
}

// User Biodata Purchase Model
model UserBiodata {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  templateId    String    @map("template_id")
  template      BiodataTemplate @relation(fields: [templateId], references: [id], onDelete: Restrict)

  purchaseDate  DateTime  @default(now()) @map("purchase_date")
  pricePaid     Float     @map("price_paid")
  downloadCount Int       @default(0) @map("download_count")
  lastDownload  DateTime? @map("last_download")
  transactionId String?   @map("transaction_id")

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@unique([userId, templateId])
  @@map("user_biodatas")
}

// Spotlight Feature Model
model SpotlightFeature {
  id            String    @id @default(cuid())
  name          String
  description   String?
  price         Float
  discountPercent Int?    @map("discount_percent")
  discountedPrice Float?  @map("discounted_price")
  defaultCount  Int       @default(1) @map("default_count")
  durationHours Int       @default(24) @map("duration_hours")
  isActive      Boolean   @default(true) @map("is_active")

  // Relationships
  userSpotlights UserSpotlight[]

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("spotlight_features")
}

// User Spotlight Purchase Model
model UserSpotlight {
  id            String    @id @default(cuid())
  userId        String    @map("user_id")
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  spotlightId   String    @map("spotlight_id")
  spotlight     SpotlightFeature @relation(fields: [spotlightId], references: [id], onDelete: Restrict)

  purchaseDate  DateTime  @default(now()) @map("purchase_date")
  pricePaid     Float     @map("price_paid")
  availableCount Int      @default(0) @map("available_count")
  usedCount     Int       @default(0) @map("used_count")
  transactionId String?   @map("transaction_id")

  // Current active spotlight
  isActive      Boolean   @default(false) @map("is_active")
  startTime     DateTime? @map("start_time")
  endTime       DateTime? @map("end_time")

  // Relationships
  activations   SpotlightActivation[]

  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  @@map("user_spotlights")
}

// Spotlight Activation History
model SpotlightActivation {
  id            String    @id @default(cuid())
  userSpotlightId String  @map("user_spotlight_id")
  userSpotlight UserSpotlight @relation(fields: [userSpotlightId], references: [id], onDelete: Cascade)
  userId        String    @map("user_id")

  startTime     DateTime  @map("start_time")
  endTime       DateTime  @map("end_time")
  isActive      Boolean   @default(true) @map("is_active")

  // Analytics
  impressions   Int       @default(0)
  clicks        Int       @default(0)

  createdAt     DateTime  @default(now()) @map("created_at")

  @@map("spotlight_activations")
}
