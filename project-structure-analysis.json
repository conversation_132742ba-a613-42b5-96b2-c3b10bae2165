{"timestamp": "2025-06-02T06:56:49.744Z", "totalFiles": 794, "filesByExtension": {"no-extension": 5, ".local": 1, ".js": 537, ".md": 31, ".mjs": 1, ".json": 74, ".ico": 1, ".svg": 7, ".jpg": 5, ".css": 44, ".html": 16, ".ps1": 7, ".bat": 2, ".backup": 1, ".example": 1, ".conf": 1, ".pt": 2, ".prisma": 5, ".sql": 19, ".toml": 1, ".backup_20250509_203953": 1, ".txt": 1, ".py": 27, ".pyc": 2, ".bak": 1, ".code-workspace": 1}, "duplicates": [{"hash": "19dc6dd7c9ff50c38cdc7d912db8a9ef", "files": ["vaivahik-nextjs\\public\\mock-data\\admin\\dashboard\\recent-activity.json", "vaivahik-nextjs\\public\\mock-data\\api\\admin\\dashboard\\recent-activity\\index.json", "vaivahik-nextjs\\public\\mock-data\\api\\admin\\dashboard\\recent-activity.json", "vaivahik-nextjs\\public\\mock-data\\api\\api\\admin\\dashboard\\recent-activity.json"], "size": 1515}, {"hash": "16bc5d8f7fbbb85597c9e8410ab59358", "files": ["vaivahik-nextjs\\public\\mock-data\\admin\\dashboard\\recent-users.json", "vaivahik-nextjs\\public\\mock-data\\api\\admin\\dashboard\\recent-users\\index.json", "vaivahik-nextjs\\public\\mock-data\\api\\admin\\dashboard\\recent-users.json", "vaivahik-nextjs\\public\\mock-data\\api\\api\\admin\\dashboard\\recent-users.json"], "size": 1450}, {"hash": "bbbdcae3a27c5436083ebdf0dc540bfd", "files": ["vaivahik-nextjs\\public\\mock-data\\api\\admin\\biodata-templates\\index.json", "vaivahik-nextjs\\public\\mock-data\\api\\api\\admin\\biodata-templates.json"], "size": 6392}, {"hash": "2112cf64551bc93095312066cd0e0130", "files": ["vaivahik-nextjs\\public\\mock-data\\api\\admin\\dashboard\\index.json", "vaivahik-nextjs\\public\\mock-data\\api\\admin\\dashboard.json", "vaivahik-nextjs\\public\\mock-data\\api\\api\\admin\\dashboard.json"], "size": 475}, {"hash": "5fc4928679e85255199195536ae73caf", "files": ["vaivahik-nextjs\\public\\mock-data\\api\\admin\\referral-programs\\index.json", "vaivahik-nextjs\\public\\mock-data\\api\\api\\admin\\referral-programs.json"], "size": 5521}, {"hash": "29f21974115d40d992bf99373f7014c6", "files": ["vaivahik-nextjs\\public\\mock-data\\api\\admin\\reported-profiles\\index.json", "vaivahik-nextjs\\public\\mock-data\\api\\api\\admin\\reported-profiles.json"], "size": 5597}, {"hash": "93238d462d864f20b5ed37d9a85259f4", "files": ["vaivahik-nextjs\\public\\mock-data\\api\\admin\\users\\index.json", "vaivahik-nextjs\\public\\mock-data\\api\\api\\admin\\users.json"], "size": 4154}, {"hash": "22a8c9b6a94ed30cfff9e3394d00d0f4", "files": ["vaivahik-nextjs\\public\\mock-data\\api\\admin\\verification-queue\\index.json", "vaivahik-nextjs\\public\\mock-data\\api\\api\\admin\\verification-queue.json"], "size": 4847}, {"hash": "ab0fa0311f7466a466d5c56e61b362a5", "files": ["vaivahik-nextjs\\public\\styles\\admin-responsive.css", "vaivahik-nextjs\\src\\styles\\admin-responsive.css"], "size": 4632}, {"hash": "558a96d15ab39fe0e467f45dc1cdb1f4", "files": ["vaivahik-nextjs\\public\\styles\\premium-plans-offers.css", "vaivahik-nextjs\\src\\styles\\premium-plans-offers.css"], "size": 2220}, {"hash": "18a634d94f5c4d150e84bcb11a71bca1", "files": ["vaivahik-nextjs\\public\\styles\\referral-programs.css", "vaivahik-nextjs\\src\\styles\\referral-programs.css"], "size": 8068}, {"hash": "255d6771b1570a69d1a3c826538615c0", "files": ["vaivahik-nextjs\\src\\components\\common\\FilterChips.js", "vaivahik-nextjs\\src\\website\\components\\common\\FilterChips.js"], "size": 8364}, {"hash": "49a77741d48085b71f238bb531395be2", "files": ["vaivahik-nextjs\\src\\components\\search\\FilterChips.js", "vaivahik-nextjs\\src\\website\\search\\components\\FilterChips.js"], "size": 7304}, {"hash": "04c9ae2ed66506d6a38ed342cf232313", "files": ["vaivahik-nextjs\\src\\components\\search\\HeightRangeSelector.js", "vaivahik-nextjs\\src\\website\\search\\components\\HeightRangeSelector.js"], "size": 8655}, {"hash": "59c5b3f6df67b74e0d05339c7460705e", "files": ["vaivahik-nextjs\\src\\utils\\heightUtils.js", "vaivahik-nextjs\\src\\website\\search\\utils\\heightUtils.js"], "size": 4427}, {"hash": "8c6030eb7c5eecfa6af84efbf69507de", "files": ["vaivahik-backend\\test-results\\get--admin-premium-plans-error.json", "vaivahik-backend\\test-results\\get--admin-reported-profiles-error.json", "vaivahik-backend\\test-results\\get--admin-success-stories-error.json", "vaivahik-backend\\test-results\\get--admin-users-error.json", "vaivahik-backend\\test-results\\get--admin-verification-queue-error.json", "vaivahik-backend\\test-results\\get--feature-flags-error.json", "vaivahik-backend\\test-results\\get--users-matches-error.json", "vaivahik-backend\\test-results\\get--users-profiles-error.json", "vaivahik-backend\\test-results\\post--auth-login-error.json", "vaivahik-backend\\test-results\\post--auth-register-error.json"], "size": 37}], "namingIssues": [{"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\ErrorMonitoring", "issue": "Mixed case in directory name", "recommendation": "Rename to errormonitoring"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\featureManagement", "issue": "Mixed case in directory name", "recommendation": "Rename to featuremanagement"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\preferenceConfig", "issue": "Mixed case in directory name", "recommendation": "Rename to preferenceconfig"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\preferenceConfig", "issue": "Mixed case in directory name", "recommendation": "Rename to preferenceconfig"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\preferenceConfig", "issue": "Mixed case in directory name", "recommendation": "Rename to preferenceconfig"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\preferenceConfig", "issue": "Mixed case in directory name", "recommendation": "Rename to preferenceconfig"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\preferenceConfig", "issue": "Mixed case in directory name", "recommendation": "Rename to preferenceconfig"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\preferenceConfig", "issue": "Mixed case in directory name", "recommendation": "Rename to preferenceconfig"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\preferenceConfig", "issue": "Mixed case in directory name", "recommendation": "Rename to preferenceconfig"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\preferenceConfig", "issue": "Mixed case in directory name", "recommendation": "Rename to preferenceconfig"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\textModeration", "issue": "Mixed case in directory name", "recommendation": "Rename to textmoderation"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\textModeration", "issue": "Mixed case in directory name", "recommendation": "Rename to textmoderation"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\textModeration", "issue": "Mixed case in directory name", "recommendation": "Rename to textmoderation"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\textModeration", "issue": "Mixed case in directory name", "recommendation": "Rename to textmoderation"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\textModeration", "issue": "Mixed case in directory name", "recommendation": "Rename to textmoderation"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\textModeration", "issue": "Mixed case in directory name", "recommendation": "Rename to textmoderation"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\components\\admin\\textModeration", "issue": "Mixed case in directory name", "recommendation": "Rename to textmoderation"}, {"type": "directory", "path": "vaivahik-nextjs\\src\\pages\\api\\admin\\photo-moderation\\photos\\[photoId]", "issue": "Mixed case in directory name", "recommendation": "Rename to [photoid]"}], "potentialUnused": []}