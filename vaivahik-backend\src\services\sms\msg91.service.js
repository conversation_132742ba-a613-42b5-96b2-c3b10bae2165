/**
 * MSG91 SMS Service
 *
 * This service handles sending SMS messages via MSG91, specifically for OTP verification.
 * It uses the approved DLT template for sending OTP messages.
 *
 * Documentation: https://docs.msg91.com/reference/send-otp-1
 */

const axios = require('axios');
const logger = require('../../utils/logger');

// Configuration
let config = {
  apiKey: process.env.MSG91_API_KEY,
  senderId: process.env.MSG91_SENDER_ID,
  dltTemplateId: process.env.MSG91_DLT_TEMPLATE_ID,
  dltPeId: process.env.MSG91_DLT_PE_ID, // Principal Entity ID
  otpLength: 6, // Changed to 6 digits for better security
  otpExpiry: 15, // minutes (MSG91 minimum requirement)
  baseUrl: 'https://control.msg91.com/api',
  useTemplate: true, // Whether to use DLT template or not
  otpTemplate: '##OTP## is the OTP to verify your mobile number -Maratha Wedding'
};

/**
 * Initialize the MSG91 service with configuration
 * @param {Object} options - Configuration options
 */
const initialize = (options = {}) => {
  config = { ...config, ...options };

  // Validate required configuration
  if (!config.apiKey) {
    console.error('MSG91 API key is required');
  }

  if (!config.senderId) {
    console.error('MSG91 Sender ID is required');
  }

  if (!config.dltTemplateId) {
    console.error('MSG91 DLT Template ID is required');
  }

  console.log('MSG91 service initialized');
};

/**
 * Send OTP via MSG91 (Updated with working solution)
 * @param {string} phone - Phone number to send OTP to (with country code)
 * @param {string} otp - OTP to send (optional - MSG91 will generate if not provided)
 * @returns {Promise<Object>} Response from MSG91
 */
const sendOtp = async (phone, otp = null) => {
  try {
    // Validate phone number
    if (!phone) {
      throw new Error('Phone number is required');
    }

    // Format phone number (ensure it has country code)
    const formattedPhone = formatPhoneNumber(phone);

    // Use the working MSG91 v5 OTP API (from your snippets)
    const requestOptions = {
      method: 'POST',
      url: 'https://control.msg91.com/api/v5/otp',
      params: {
        otp_expiry: config.otpExpiry.toString(),
        template_id: config.dltTemplateId,
        mobile: formattedPhone,
        authkey: config.apiKey,
        realTimeResponse: '1', // Key for delivery status
        otp_length: config.otpLength.toString() // Try to set 6-digit OTP
      },
      headers: {
        'Content-Type': 'application/json'
      },
      data: {}
    };

    // Note: MSG91 v5 OTP API generates OTP automatically
    // Template parameters are handled via template_id in the request

    logger.info(`Sending OTP to ${formattedPhone} via MSG91 v5 API`);

    // Send OTP using the working MSG91 v5 API
    const response = await axios.request(requestOptions);

    // Check if the response indicates success (MSG91 v5 format)
    const isSuccess = response.data && response.data.type === 'success';

    if (isSuccess) {
      logger.info(`OTP sent successfully to ${formattedPhone}`);
      return {
        success: true,
        message: 'OTP sent successfully',
        data: response.data
      };
    } else {
      logger.error(`Failed to send OTP to ${formattedPhone}: ${JSON.stringify(response.data)}`);
      return {
        success: false,
        message: response.data?.message || 'Failed to send OTP',
        data: response.data
      };
    }
  } catch (error) {
    logger.error('Error sending OTP via MSG91:', error);
    return {
      success: false,
      message: error.message || 'Failed to send OTP',
      error: error
    };
  }
};

/**
 * Verify OTP via MSG91
 * @param {string} phone - Phone number to verify OTP for (with country code)
 * @param {string} otp - OTP to verify
 * @returns {Promise<Object>} Response from MSG91
 */
const verifyOtp = async (phone, otp) => {
  try {
    // Validate inputs
    if (!phone || !otp) {
      throw new Error('Phone number and OTP are required');
    }

    // Format phone number
    const formattedPhone = formatPhoneNumber(phone);

    // Use the working MSG91 v5 verify API (from your snippets)
    const requestOptions = {
      method: 'GET',
      url: 'https://control.msg91.com/api/v5/otp/verify',
      params: {
        otp: otp,
        mobile: formattedPhone
      },
      headers: {
        authkey: config.apiKey
      }
    };

    logger.info(`Verifying OTP for ${formattedPhone} via MSG91 v5 API`);

    // Verify OTP using the working MSG91 v5 API
    const response = await axios.request(requestOptions);

    // Check if verification was successful (MSG91 v5 format)
    const isVerified = response.data && response.data.type === 'success';

    if (isVerified) {
      logger.info(`OTP verified successfully for ${formattedPhone}`);
    } else {
      logger.warn(`OTP verification failed for ${formattedPhone}: ${JSON.stringify(response.data)}`);
    }

    return {
      success: isVerified,
      message: isVerified ? 'OTP verified successfully' : 'Invalid or expired OTP',
      data: response.data
    };
  } catch (error) {
    logger.error('Error verifying OTP via MSG91:', error);
    return {
      success: false,
      message: error.message || 'Failed to verify OTP',
      error: error
    };
  }
};

/**
 * Resend OTP via MSG91
 * @param {string} phone - Phone number to resend OTP to (with country code)
 * @param {string} retryType - Type of retry: 'text' or 'voice'
 * @returns {Promise<Object>} Response from MSG91
 */
const resendOtp = async (phone, retryType = 'text') => {
  try {
    // Validate phone number
    if (!phone) {
      throw new Error('Phone number is required');
    }

    // Format phone number
    const formattedPhone = formatPhoneNumber(phone);

    // Validate retry type
    if (retryType !== 'text' && retryType !== 'voice') {
      logger.warn(`Invalid retry type: ${retryType}. Using 'text' as default.`);
      retryType = 'text';
    }

    // Use the working MSG91 v5 resend API (from your snippets)
    const requestOptions = {
      method: 'GET',
      url: 'https://control.msg91.com/api/v5/otp/retry',
      params: {
        authkey: config.apiKey,
        retrytype: retryType,
        mobile: formattedPhone
      }
    };

    logger.info(`Resending OTP to ${formattedPhone} via MSG91 v5 API (${retryType} mode)`);

    // Resend OTP using the working MSG91 v5 API
    const response = await axios.request(requestOptions);

    // Check if the response indicates success (MSG91 v5 format)
    const isSuccess = response.data && response.data.type === 'success';

    if (isSuccess) {
      logger.info(`OTP resent successfully to ${formattedPhone} (${retryType} mode)`);
      return {
        success: true,
        message: `OTP resent successfully via ${retryType}`,
        data: response.data
      };
    } else {
      logger.error(`Failed to resend OTP to ${formattedPhone}: ${JSON.stringify(response.data)}`);
      return {
        success: false,
        message: response.data?.message || 'Failed to resend OTP',
        data: response.data
      };
    }
  } catch (error) {
    logger.error('Error resending OTP via MSG91:', error);
    return {
      success: false,
      message: error.message || 'Failed to resend OTP',
      error: error
    };
  }
};

/**
 * Format phone number to ensure it has country code
 * @param {string} phone - Phone number
 * @returns {string} Formatted phone number
 */
const formatPhoneNumber = (phone) => {
  // Remove any non-digit characters
  let cleaned = phone.replace(/\D/g, '');

  // If the number starts with a plus sign, remove it
  if (phone.startsWith('+')) {
    cleaned = phone.substring(1).replace(/\D/g, '');
  }

  // If the number doesn't start with country code (e.g., 91 for India)
  // add the default country code (91 for India)
  if (!cleaned.startsWith('91') && cleaned.length === 10) {
    cleaned = '91' + cleaned;
  }

  return cleaned;
};

/**
 * Generate a random OTP
 * @param {number} length - Length of the OTP (default: from config)
 * @returns {string} Generated OTP
 */
const generateOtp = (length = config.otpLength) => {
  // Ensure length is a valid number
  const otpLength = parseInt(length) || 6;

  // Generate a random OTP of the specified length
  let otp = '';
  for (let i = 0; i < otpLength; i++) {
    otp += Math.floor(Math.random() * 10);
  }

  return otp;
};

module.exports = {
  initialize,
  sendOtp,
  verifyOtp,
  resendOtp,
  generateOtp,
  formatPhoneNumber
};
